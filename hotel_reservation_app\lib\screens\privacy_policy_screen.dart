import 'package:flutter/material.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  final List<PolicySection> _policySections = [
    PolicySection(
      title: '1. Information We Collect',
      content: '''We collect information you provide directly to us, such as when you create an account, make a reservation, or contact us for support. This may include:

• Personal information (name, email address, phone number)
• Payment information (credit card details, billing address)
• Reservation details (dates, room preferences, special requests)
• Communication records (support tickets, feedback)
• Device information (IP address, browser type, operating system)''',
    ),
    PolicySection(
      title: '2. How We Use Your Information',
      content: '''We use the information we collect to:

• Process and manage your reservations
• Provide customer support and respond to inquiries
• Send booking confirmations and important updates
• Improve our services and user experience
• Comply with legal obligations
• Prevent fraud and ensure security
• Send promotional communications (with your consent)''',
    ),
    PolicySection(
      title: '3. Information Sharing',
      content: '''We do not sell, trade, or rent your personal information to third parties. We may share your information only in the following circumstances:

• With service providers who assist us in operating our business
• When required by law or to protect our rights
• In connection with a business transfer or merger
• With your explicit consent
• To prevent fraud or security threats''',
    ),
    PolicySection(
      title: '4. Data Security',
      content: '''We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. These measures include:

• Encryption of sensitive data
• Secure server infrastructure
• Regular security audits
• Access controls and authentication
• Employee training on data protection

However, no method of transmission over the internet is 100% secure.''',
    ),
    PolicySection(
      title: '5. Your Rights',
      content: '''You have the right to:

• Access your personal information
• Correct inaccurate information
• Delete your account and data
• Opt-out of marketing communications
• Data portability
• Lodge a complaint with supervisory authorities

To exercise these rights, please contact us using the information provided in the Contact section.''',
    ),
    PolicySection(
      title: '6. Cookies and Tracking',
      content: '''We use cookies and similar technologies to:

• Remember your preferences
• Analyze app usage and performance
• Provide personalized content
• Ensure security

You can control cookie settings through your device settings, but some features may not function properly if cookies are disabled.''',
    ),
    PolicySection(
      title: '7. Children\'s Privacy',
      content: '''Our services are not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. If you are a parent or guardian and believe your child has provided us with personal information, please contact us immediately.''',
    ),
    PolicySection(
      title: '8. International Data Transfers',
      content: '''Your information may be transferred to and processed in countries other than your own. We ensure that such transfers comply with applicable data protection laws and implement appropriate safeguards to protect your information.''',
    ),
    PolicySection(
      title: '9. Changes to This Policy',
      content: '''We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new policy in the app and updating the "Last Updated" date. Your continued use of our services after such changes constitutes acceptance of the updated policy.''',
    ),
    PolicySection(
      title: '10. Contact Information',
      content: '''If you have any questions about this Privacy Policy or our data practices, please contact us at:

Email: <EMAIL>
Phone: +****************
Address: 123 Hotel Street, City, State 12345

Data Protection Officer: <EMAIL>''',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.privacy_tip,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Privacy Policy',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Last Updated: December 2024',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Introduction
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Introduction',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Hotel Reservation App ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application and services.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'By using our app, you agree to the collection and use of information in accordance with this policy.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.primary,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Policy Sections
            ...(_policySections.map((section) => _buildPolicySection(section)).toList()),

            const SizedBox(height: 24),

            // Footer
            Card(
              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Icon(
                      Icons.security,
                      size: 32,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Your Privacy Matters',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'We are committed to protecting your personal information and maintaining your trust. If you have any questions or concerns about our privacy practices, please don\'t hesitate to contact us.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildPolicySection(PolicySection section) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        title: Text(
          section.title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              section.content,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                height: 1.6,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class PolicySection {
  final String title;
  final String content;

  PolicySection({
    required this.title,
    required this.content,
  });
}
