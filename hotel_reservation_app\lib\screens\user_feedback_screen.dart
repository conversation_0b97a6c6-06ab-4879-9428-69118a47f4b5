import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../services/notification_service.dart';
import '../models/feedback_model.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';

class UserFeedbackScreen extends StatefulWidget {
  const UserFeedbackScreen({super.key});

  @override
  State<UserFeedbackScreen> createState() => _UserFeedbackScreenState();
}

class _UserFeedbackScreenState extends State<UserFeedbackScreen> {
  final _formKey = GlobalKey<FormState>();
  final _commentController = TextEditingController();
  int _rating = 0;
  bool _isLoading = false;
  List<UserFeedback> _userFeedbacks = [];
  bool _isLoadingFeedbacks = true;

  @override
  void initState() {
    super.initState();
    _loadUserFeedbacks();
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _loadUserFeedbacks() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final databaseService = Provider.of<DatabaseService>(context, listen: false);

    final user = authService.currentUser;
    if (user != null) {
      final feedbacks = await databaseService.getFeedbacksByUserId(user.id!);
      setState(() {
        _userFeedbacks = feedbacks;
        _isLoadingFeedbacks = false;
      });
    } else {
      setState(() => _isLoadingFeedbacks = false);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _submitFeedback() async {
    if (!_formKey.currentState!.validate()) return;

    if (_rating == 0) {
      _showSnackBar('Please select a rating', isError: true);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final databaseService = Provider.of<DatabaseService>(context, listen: false);
      final notificationService = Provider.of<NotificationService>(context, listen: false);

      final user = authService.currentUser;
      if (user == null) {
        _showSnackBar('Please log in to submit feedback', isError: true);
        setState(() => _isLoading = false);
        return;
      }

      final feedback = UserFeedback(
        userId: user.id!,
        rating: _rating,
        comment: _commentController.text.trim().isEmpty ? null : _commentController.text.trim(),
        createdAt: DateTime.now(),
      );

      final feedbackId = await databaseService.createFeedback(feedback);

      if (feedbackId > 0) {
        // Show thank you notification
        await notificationService.showFeedbackThankYou();

        setState(() => _isLoading = false);
        _showSnackBar('Thank you for your feedback!');

        // Reset form
        _commentController.clear();
        setState(() => _rating = 0);

        // Reload feedbacks
        _loadUserFeedbacks();
      } else {
        setState(() => _isLoading = false);
        _showSnackBar('Failed to submit feedback. Please try again.', isError: true);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      _showSnackBar('Error submitting feedback: $e', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Feedback'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.feedback,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Share Your Experience',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Your feedback helps us improve our service',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Feedback Form
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Rate Your Experience',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Star Rating
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(5, (index) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _rating = index + 1;
                              });
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              child: Icon(
                                index < _rating ? Icons.star : Icons.star_border,
                                size: 40,
                                color: index < _rating
                                    ? Colors.amber
                                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                              ),
                            ),
                          );
                        }),
                      ),

                      if (_rating > 0) ...[
                        const SizedBox(height: 8),
                        Center(
                          child: Text(
                            _getRatingText(_rating),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],

                      const SizedBox(height: 24),

                      // Comment Field
                      Text(
                        'Additional Comments (Optional)',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      CustomTextField(
                        controller: _commentController,
                        labelText: 'Your Comments',
                        hintText: 'Tell us about your experience...',
                        maxLines: 4,
                      ),

                      const SizedBox(height: 24),

                      // Submit Button
                      CustomButton(
                        text: 'Submit Feedback',
                        onPressed: _isLoading ? null : _submitFeedback,
                        isLoading: _isLoading,
                        width: double.infinity,
                        icon: Icons.send,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Previous Feedbacks
            Text(
              'Your Previous Feedback',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            _isLoadingFeedbacks
                ? const Center(child: CircularProgressIndicator())
                : _userFeedbacks.isEmpty
                    ? _buildEmptyFeedbackState()
                    : _buildFeedbacksList(),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyFeedbackState() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.feedback_outlined,
              size: 48,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No Feedback Yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your submitted feedback will appear here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedbacksList() {
    return Column(
      children: _userFeedbacks.map((feedback) => _buildFeedbackCard(feedback)).toList(),
    );
  }

  Widget _buildFeedbackCard(UserFeedback feedback) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Rating and Date
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    ...List.generate(5, (index) {
                      return Icon(
                        index < feedback.rating ? Icons.star : Icons.star_border,
                        size: 20,
                        color: index < feedback.rating
                            ? Colors.amber
                            : Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                      );
                    }),
                    const SizedBox(width: 8),
                    Text(
                      '(${feedback.rating}/5)',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Text(
                  _formatDate(feedback.createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),

            if (feedback.comment != null && feedback.comment!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                feedback.comment!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getRatingText(int rating) {
    switch (rating) {
      case 1:
        return 'Poor';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Very Good';
      case 5:
        return 'Excellent';
      default:
        return '';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
