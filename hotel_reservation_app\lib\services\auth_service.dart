import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import '../models/user_model.dart' as app_models;
import 'firestore_service.dart';
import 'email_service.dart';

class AuthService extends ChangeNotifier {
  // Firebase instances
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirestoreService _firestoreService = FirestoreService();
  final EmailService _emailService = EmailService();

  // Current user state
  app_models.User? _currentUser;
  User? _firebaseUser;
  bool _isLoading = false;
  bool _isEmailVerified = false;

  // Getters
  app_models.User? get currentUser => _currentUser;
  User? get firebaseUser => _firebaseUser;
  bool get isLoggedIn => _firebaseUser != null && _isEmailVerified;
  bool get isLoading => _isLoading;
  bool get isEmailVerified => _isEmailVerified;

  AuthService() {
    _initializeAuthListener();
  }

  // Initialize Firebase Auth state listener
  void _initializeAuthListener() {
    _firebaseAuth.authStateChanges().listen((User? user) async {
      _firebaseUser = user;
      if (user != null) {
        await _loadUserData(user.uid);
        _isEmailVerified = user.emailVerified;
      } else {
        _currentUser = null;
        _isEmailVerified = false;
      }
      notifyListeners();
    });
  }

  // Load user data from Firestore
  Future<void> _loadUserData(String uid) async {
    try {
      final userData = await _firestoreService.getUser(uid);
      if (userData != null) {
        _currentUser = userData;
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  // Hash password for security
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Validate password strength
  bool _isPasswordStrong(String password) {
    if (password.length < 8) return false;
    if (!password.contains(RegExp(r'[A-Z]'))) return false;
    if (!password.contains(RegExp(r'[a-z]'))) return false;
    if (!password.contains(RegExp(r'[0-9]'))) return false;
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) return false;
    return true;
  }

  // Email/Password Login
  Future<AuthResult> loginWithEmailPassword(String email, String password) async {
    _setLoading(true);
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        if (!credential.user!.emailVerified) {
          _setLoading(false);
          return AuthResult.error('Please verify your email before logging in');
        }

        _setLoading(false);
        return AuthResult.success('Login successful');
      } else {
        _setLoading(false);
        return AuthResult.error('Login failed');
      }
    } on FirebaseAuthException catch (e) {
      _setLoading(false);
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Login failed: $e');
    }
  }

  // Google Sign In
  Future<AuthResult> signInWithGoogle() async {
    _setLoading(true);
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        _setLoading(false);
        return AuthResult.error('Google sign in cancelled');
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        // Create user profile if first time
        await _createUserProfileIfNeeded(userCredential.user!);
        _setLoading(false);
        return AuthResult.success('Google sign in successful');
      } else {
        _setLoading(false);
        return AuthResult.error('Google sign in failed');
      }
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Google sign in failed: $e');
    }
  }

  // Register new user with email verification
  Future<AuthResult> registerWithEmailPassword(
    String name,
    String email,
    String password,
    String phone
  ) async {
    _setLoading(true);
    try {
      // Validate password strength
      if (!_isPasswordStrong(password)) {
        _setLoading(false);
        return AuthResult.error(
          'Password must be at least 8 characters with uppercase, lowercase, number, and special character'
        );
      }

      // Create Firebase user
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName(name);

        // Send email verification
        await credential.user!.sendEmailVerification();

        // Create user profile in Firestore
        final user = app_models.User(
          id: credential.user!.uid,
          name: name,
          email: email.trim(),
          phone: phone.trim().isEmpty ? null : phone.trim(),
          createdAt: DateTime.now(),
          isEmailVerified: false,
          profileImageUrl: null,
        );

        await _firestoreService.createUser(user);

        // Send welcome email
        await _emailService.sendWelcomeEmail(email, name);

        _setLoading(false);
        return AuthResult.success(
          'Registration successful! Please check your email to verify your account.'
        );
      } else {
        _setLoading(false);
        return AuthResult.error('Registration failed');
      }
    } on FirebaseAuthException catch (e) {
      _setLoading(false);
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Registration failed: $e');
    }
  }

  // Reset password
  Future<AuthResult> resetPassword(String email) async {
    _setLoading(true);
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email.trim());

      // Send custom password reset email
      await _emailService.sendPasswordResetEmail(email.trim());

      _setLoading(false);
      return AuthResult.success('Password reset instructions sent to your email');
    } on FirebaseAuthException catch (e) {
      _setLoading(false);
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Password reset failed: $e');
    }
  }

  // Resend email verification
  Future<AuthResult> resendEmailVerification() async {
    _setLoading(true);
    try {
      if (_firebaseUser != null && !_firebaseUser!.emailVerified) {
        await _firebaseUser!.sendEmailVerification();
        _setLoading(false);
        return AuthResult.success('Verification email sent');
      } else {
        _setLoading(false);
        return AuthResult.error('No user found or email already verified');
      }
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Failed to send verification email: $e');
    }
  }

  // Check email verification status
  Future<AuthResult> checkEmailVerification() async {
    _setLoading(true);
    try {
      await _firebaseUser?.reload();
      final user = _firebaseAuth.currentUser;
      if (user != null && user.emailVerified) {
        _isEmailVerified = true;
        // Update Firestore
        await _firestoreService.updateUserEmailVerification(user.uid, true);
        _setLoading(false);
        notifyListeners();
        return AuthResult.success('Email verified successfully');
      } else {
        _setLoading(false);
        return AuthResult.error('Email not yet verified');
      }
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Failed to check verification: $e');
    }
  }

  // Update user profile
  Future<AuthResult> updateProfile(app_models.User updatedUser) async {
    _setLoading(true);
    try {
      if (_firebaseUser != null) {
        // Update Firebase display name if changed
        if (updatedUser.name != _firebaseUser!.displayName) {
          await _firebaseUser!.updateDisplayName(updatedUser.name);
        }

        // Update Firestore
        await _firestoreService.updateUser(updatedUser);
        _currentUser = updatedUser;

        _setLoading(false);
        return AuthResult.success('Profile updated successfully');
      } else {
        _setLoading(false);
        return AuthResult.error('No user logged in');
      }
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Profile update failed: $e');
    }
  }

  // Change password
  Future<AuthResult> changePassword(String currentPassword, String newPassword) async {
    _setLoading(true);
    try {
      if (_firebaseUser == null) {
        _setLoading(false);
        return AuthResult.error('No user logged in');
      }

      // Validate new password strength
      if (!_isPasswordStrong(newPassword)) {
        _setLoading(false);
        return AuthResult.error(
          'New password must be at least 8 characters with uppercase, lowercase, number, and special character'
        );
      }

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: _firebaseUser!.email!,
        password: currentPassword,
      );

      await _firebaseUser!.reauthenticateWithCredential(credential);

      // Update password
      await _firebaseUser!.updatePassword(newPassword);

      _setLoading(false);
      return AuthResult.success('Password changed successfully');
    } on FirebaseAuthException catch (e) {
      _setLoading(false);
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Password change failed: $e');
    }
  }

  // Delete account
  Future<AuthResult> deleteAccount(String password) async {
    _setLoading(true);
    try {
      if (_firebaseUser == null) {
        _setLoading(false);
        return AuthResult.error('No user logged in');
      }

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: _firebaseUser!.email!,
        password: password,
      );

      await _firebaseUser!.reauthenticateWithCredential(credential);

      // Delete user data from Firestore
      await _firestoreService.deleteUser(_firebaseUser!.uid);

      // Delete Firebase user
      await _firebaseUser!.delete();

      _setLoading(false);
      return AuthResult.success('Account deleted successfully');
    } on FirebaseAuthException catch (e) {
      _setLoading(false);
      return AuthResult.error(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Account deletion failed: $e');
    }
  }

  // Logout user
  Future<void> logout() async {
    _setLoading(true);
    try {
      await _googleSignIn.signOut();
      await _firebaseAuth.signOut();

      _currentUser = null;
      _firebaseUser = null;
      _isEmailVerified = false;
    } catch (e) {
      debugPrint('Error during logout: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Create user profile if needed (for social login)
  Future<void> _createUserProfileIfNeeded(User firebaseUser) async {
    try {
      final existingUser = await _firestoreService.getUser(firebaseUser.uid);
      if (existingUser == null) {
        final user = app_models.User(
          id: firebaseUser.uid,
          name: firebaseUser.displayName ?? 'User',
          email: firebaseUser.email!,
          phone: firebaseUser.phoneNumber,
          createdAt: DateTime.now(),
          isEmailVerified: firebaseUser.emailVerified,
          profileImageUrl: firebaseUser.photoURL,
        );

        await _firestoreService.createUser(user);
      }
    } catch (e) {
      debugPrint('Error creating user profile: $e');
    }
  }

  // Get user-friendly error messages
  String _getFirebaseErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'No user found with this email address';
      case 'wrong-password':
        return 'Incorrect password';
      case 'email-already-in-use':
        return 'An account already exists with this email';
      case 'weak-password':
        return 'Password is too weak';
      case 'invalid-email':
        return 'Invalid email address';
      case 'user-disabled':
        return 'This account has been disabled';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later';
      case 'operation-not-allowed':
        return 'This operation is not allowed';
      case 'requires-recent-login':
        return 'Please log in again to perform this action';
      default:
        return 'An error occurred. Please try again';
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
}

class AuthResult {
  final bool isSuccess;
  final String message;

  AuthResult._(this.isSuccess, this.message);

  factory AuthResult.success(String message) => AuthResult._(true, message);
  factory AuthResult.error(String message) => AuthResult._(false, message);
}
