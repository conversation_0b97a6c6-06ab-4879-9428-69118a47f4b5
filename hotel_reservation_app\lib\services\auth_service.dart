import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import 'database_service.dart';

class AuthService extends ChangeNotifier {
  User? _currentUser;
  bool _isLoggedIn = false;
  bool _isLoading = false;

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;

  final DatabaseService _databaseService = DatabaseService.instance;

  AuthService() {
    _checkLoginStatus();
  }

  // Check if user is already logged in
  Future<void> _checkLoginStatus() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt('user_id');
      
      if (userId != null) {
        final user = await _databaseService.getUserById(userId);
        if (user != null) {
          _currentUser = user;
          _isLoggedIn = true;
        }
      }
    } catch (e) {
      debugPrint('Error checking login status: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Login user
  Future<AuthResult> login(String email, String password) async {
    _setLoading(true);
    try {
      final user = await _databaseService.loginUser(email, password);
      
      if (user != null) {
        _currentUser = user;
        _isLoggedIn = true;
        
        // Save login state
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt('user_id', user.id!);
        
        _setLoading(false);
        return AuthResult.success('Login successful');
      } else {
        _setLoading(false);
        return AuthResult.error('Invalid email or password');
      }
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Login failed: $e');
    }
  }

  // Register new user
  Future<AuthResult> register(String name, String email, String password, String phone) async {
    _setLoading(true);
    try {
      // Check if user already exists
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser != null) {
        _setLoading(false);
        return AuthResult.error('User with this email already exists');
      }

      // Create new user
      final user = User(
        name: name,
        email: email,
        password: password,
        phone: phone,
        createdAt: DateTime.now(),
      );

      final userId = await _databaseService.createUser(user);
      if (userId > 0) {
        user.id = userId;
        _currentUser = user;
        _isLoggedIn = true;
        
        // Save login state
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt('user_id', userId);
        
        _setLoading(false);
        return AuthResult.success('Registration successful');
      } else {
        _setLoading(false);
        return AuthResult.error('Registration failed');
      }
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Registration failed: $e');
    }
  }

  // Reset password
  Future<AuthResult> resetPassword(String email) async {
    _setLoading(true);
    try {
      final user = await _databaseService.getUserByEmail(email);
      if (user != null) {
        // In a real app, you would send an email with reset instructions
        // For now, we'll just return success
        _setLoading(false);
        return AuthResult.success('Password reset instructions sent to your email');
      } else {
        _setLoading(false);
        return AuthResult.error('No user found with this email');
      }
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Password reset failed: $e');
    }
  }

  // Update user profile
  Future<AuthResult> updateProfile(User updatedUser) async {
    _setLoading(true);
    try {
      final success = await _databaseService.updateUser(updatedUser);
      if (success) {
        _currentUser = updatedUser;
        _setLoading(false);
        return AuthResult.success('Profile updated successfully');
      } else {
        _setLoading(false);
        return AuthResult.error('Failed to update profile');
      }
    } catch (e) {
      _setLoading(false);
      return AuthResult.error('Profile update failed: $e');
    }
  }

  // Logout user
  Future<void> logout() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_id');
      
      _currentUser = null;
      _isLoggedIn = false;
    } catch (e) {
      debugPrint('Error during logout: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}

class AuthResult {
  final bool isSuccess;
  final String message;

  AuthResult._(this.isSuccess, this.message);

  factory AuthResult.success(String message) => AuthResult._(true, message);
  factory AuthResult.error(String message) => AuthResult._(false, message);
}
