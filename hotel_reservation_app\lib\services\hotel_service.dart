import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../models/hotel_model.dart';
import '../models/reservation_model.dart';
import 'firestore_service.dart';

class HotelService {
  static final HotelService _instance = HotelService._internal();
  static HotelService get instance => _instance;
  HotelService._internal();

  final FirestoreService _firestoreService = FirestoreService.instance;

  // Get all available hotels
  Future<List<Hotel>> getHotels({
    String? city,
    String? country,
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) async {
    try {
      List<Hotel> hotels = await _firestoreService.getHotels();
      
      // Filter by location if provided
      if (latitude != null && longitude != null && radiusKm != null) {
        hotels = hotels.where((hotel) {
          final distance = Geolocator.distanceBetween(
            latitude,
            longitude,
            hotel.latitude,
            hotel.longitude,
          ) / 1000; // Convert to km
          
          return distance <= radiusKm;
        }).toList();
      }
      
      // Filter by city if provided
      if (city != null) {
        hotels = hotels.where((hotel) => 
          hotel.city.toLowerCase().contains(city.toLowerCase())
        ).toList();
      }
      
      // Filter by country if provided
      if (country != null) {
        hotels = hotels.where((hotel) => 
          hotel.country.toLowerCase().contains(country.toLowerCase())
        ).toList();
      }
      
      // Sort by rating (highest first)
      hotels.sort((a, b) => b.rating.compareTo(a.rating));
      
      return hotels;
    } catch (e) {
      debugPrint('Error getting hotels: $e');
      return [];
    }
  }

  // Get hotel by ID
  Future<Hotel?> getHotel(String hotelId) async {
    try {
      return await _firestoreService.getHotel(hotelId);
    } catch (e) {
      debugPrint('Error getting hotel: $e');
      return null;
    }
  }

  // Search hotels by name or location
  Future<List<Hotel>> searchHotels(String query) async {
    try {
      final hotels = await _firestoreService.getHotels();
      
      return hotels.where((hotel) {
        final searchText = query.toLowerCase();
        return hotel.name.toLowerCase().contains(searchText) ||
               hotel.city.toLowerCase().contains(searchText) ||
               hotel.country.toLowerCase().contains(searchText) ||
               hotel.address.toLowerCase().contains(searchText);
      }).toList();
    } catch (e) {
      debugPrint('Error searching hotels: $e');
      return [];
    }
  }

  // Get hotels near user's location
  Future<List<Hotel>> getHotelsNearMe({double radiusKm = 50.0}) async {
    try {
      // Get user's current location
      final position = await _getCurrentPosition();
      if (position == null) return [];
      
      return await getHotels(
        latitude: position.latitude,
        longitude: position.longitude,
        radiusKm: radiusKm,
      );
    } catch (e) {
      debugPrint('Error getting hotels near me: $e');
      return [];
    }
  }

  // Get current position
  Future<Position?> _getCurrentPosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return null;
      }

      return await Geolocator.getCurrentPosition();
    } catch (e) {
      debugPrint('Error getting current position: $e');
      return null;
    }
  }

  // Check room availability
  Future<bool> checkRoomAvailability({
    required String hotelId,
    required String roomType,
    required DateTime checkIn,
    required DateTime checkOut,
  }) async {
    try {
      return await _firestoreService.checkRoomAvailability(
        hotelId,
        roomType,
        checkIn,
        checkOut,
      );
    } catch (e) {
      debugPrint('Error checking room availability: $e');
      return false;
    }
  }

  // Get available room types for a hotel
  Future<Map<String, RoomType>> getAvailableRoomTypes({
    required String hotelId,
    required DateTime checkIn,
    required DateTime checkOut,
  }) async {
    try {
      final hotel = await getHotel(hotelId);
      if (hotel == null) return {};
      
      final availableRooms = <String, RoomType>{};
      
      for (final entry in hotel.roomTypes.entries) {
        final isAvailable = await checkRoomAvailability(
          hotelId: hotelId,
          roomType: entry.key,
          checkIn: checkIn,
          checkOut: checkOut,
        );
        
        if (isAvailable) {
          availableRooms[entry.key] = entry.value;
        }
      }
      
      return availableRooms;
    } catch (e) {
      debugPrint('Error getting available room types: $e');
      return {};
    }
  }

  // Get hotel amenities
  List<String> getStandardAmenities() {
    return [
      'Free WiFi',
      'Air Conditioning',
      'Room Service',
      'Housekeeping',
      'Concierge',
      'Front Desk 24/7',
      'Elevator',
      'Parking',
      'Restaurant',
      'Bar/Lounge',
      'Fitness Center',
      'Swimming Pool',
      'Spa',
      'Business Center',
      'Meeting Rooms',
      'Laundry Service',
      'Airport Shuttle',
      'Pet Friendly',
      'Wheelchair Accessible',
      'Non-Smoking Rooms',
    ];
  }

  // Get room amenities
  List<String> getStandardRoomAmenities() {
    return [
      'Private Bathroom',
      'Shower',
      'Bathtub',
      'Hair Dryer',
      'Towels',
      'Toiletries',
      'TV',
      'Cable/Satellite TV',
      'Telephone',
      'Mini Bar',
      'Coffee/Tea Maker',
      'Safe',
      'Iron/Ironing Board',
      'Desk',
      'Seating Area',
      'Balcony',
      'City View',
      'Ocean View',
      'Mountain View',
      'Garden View',
    ];
  }

  // Calculate distance between two points
  double calculateDistance({
    required double lat1,
    required double lon1,
    required double lat2,
    required double lon2,
  }) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2) / 1000; // km
  }

  // Get address from coordinates
  Future<String?> getAddressFromCoordinates({
    required double latitude,
    required double longitude,
  }) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        latitude,
        longitude,
      );
      
      if (placemarks.isNotEmpty) {
        final place = placemarks.first;
        return '${place.street}, ${place.locality}, ${place.country}';
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting address from coordinates: $e');
      return null;
    }
  }

  // Get coordinates from address
  Future<Position?> getCoordinatesFromAddress(String address) async {
    try {
      List<Location> locations = await locationFromAddress(address);
      
      if (locations.isNotEmpty) {
        final location = locations.first;
        return Position(
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting coordinates from address: $e');
      return null;
    }
  }

  // Initialize sample hotel data (for demo purposes)
  Future<void> initializeSampleHotels() async {
    try {
      final sampleHotels = _createSampleHotels();
      
      for (final hotel in sampleHotels) {
        // Check if hotel already exists
        final existingHotel = await _firestoreService.getHotel(hotel.id!);
        if (existingHotel == null) {
          await _firestoreService._hotelsCollection.doc(hotel.id).set(hotel.toFirestoreMap());
        }
      }
      
      debugPrint('Sample hotels initialized');
    } catch (e) {
      debugPrint('Error initializing sample hotels: $e');
    }
  }

  List<Hotel> _createSampleHotels() {
    return [
      Hotel(
        id: 'hotel_1',
        name: 'Grand Plaza Hotel',
        description: 'Luxury hotel in the heart of the city with stunning views and world-class amenities.',
        address: '123 Main Street',
        city: 'New York',
        country: 'United States',
        latitude: 40.7128,
        longitude: -74.0060,
        imageUrls: [
          'https://example.com/hotel1_1.jpg',
          'https://example.com/hotel1_2.jpg',
          'https://example.com/hotel1_3.jpg',
        ],
        amenities: [
          'Free WiFi',
          'Swimming Pool',
          'Fitness Center',
          'Spa',
          'Restaurant',
          'Bar',
          'Room Service',
          'Concierge',
          'Valet Parking',
        ],
        roomTypes: {
          'Standard Room': RoomType(
            name: 'Standard Room',
            description: 'Comfortable room with city view',
            pricePerNight: 199.99,
            maxGuests: 2,
            size: 25.0,
            amenities: ['Private Bathroom', 'TV', 'Mini Bar', 'City View'],
            imageUrls: ['https://example.com/room1.jpg'],
            totalRooms: 50,
          ),
          'Deluxe Room': RoomType(
            name: 'Deluxe Room',
            description: 'Spacious room with premium amenities',
            pricePerNight: 299.99,
            maxGuests: 3,
            size: 35.0,
            amenities: ['Private Bathroom', 'TV', 'Mini Bar', 'Balcony', 'City View'],
            imageUrls: ['https://example.com/room2.jpg'],
            totalRooms: 30,
          ),
          'Suite': RoomType(
            name: 'Suite',
            description: 'Luxury suite with separate living area',
            pricePerNight: 499.99,
            maxGuests: 4,
            size: 60.0,
            amenities: ['Private Bathroom', 'TV', 'Mini Bar', 'Balcony', 'Ocean View', 'Seating Area'],
            imageUrls: ['https://example.com/room3.jpg'],
            totalRooms: 20,
          ),
        },
        rating: 4.5,
        reviewCount: 1250,
        phone: '******-123-4567',
        email: '<EMAIL>',
        website: 'https://grandplaza.com',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      // Add more sample hotels...
    ];
  }
}
