import 'package:flutter/foundation.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:dio/dio.dart';
import '../models/reservation_model.dart';

class PaymentService {
  static final PaymentService _instance = PaymentService._internal();
  static PaymentService get instance => _instance;
  PaymentService._internal();

  final Dio _dio = Dio();
  
  // Stripe configuration
  static const String _stripePublishableKey = 'pk_test_your_stripe_publishable_key_here';
  static const String _stripeSecretKey = 'sk_test_your_stripe_secret_key_here';
  static const String _stripeApiUrl = 'https://api.stripe.com/v1';

  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      Stripe.publishableKey = _stripePublishableKey;
      await Stripe.instance.applySettings();
      _isInitialized = true;
      debugPrint('Stripe initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Stripe: $e');
    }
  }

  // Create payment intent on server
  Future<Map<String, dynamic>?> _createPaymentIntent({
    required double amount,
    required String currency,
    required String customerId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _dio.post(
        '$_stripeApiUrl/payment_intents',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_stripeSecretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: {
          'amount': (amount * 100).round(), // Convert to cents
          'currency': currency.toLowerCase(),
          'customer': customerId,
          'automatic_payment_methods[enabled]': true,
          if (metadata != null)
            ...metadata.map((key, value) => MapEntry('metadata[$key]', value.toString())),
        },
      );

      return response.data;
    } catch (e) {
      debugPrint('Error creating payment intent: $e');
      return null;
    }
  }

  // Create customer on Stripe
  Future<String?> createCustomer({
    required String email,
    required String name,
    String? phone,
  }) async {
    try {
      final response = await _dio.post(
        '$_stripeApiUrl/customers',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_stripeSecretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: {
          'email': email,
          'name': name,
          if (phone != null) 'phone': phone,
        },
      );

      return response.data['id'];
    } catch (e) {
      debugPrint('Error creating customer: $e');
      return null;
    }
  }

  // Process payment for reservation
  Future<PaymentResult> processReservationPayment({
    required Reservation reservation,
    required String customerEmail,
    required String customerName,
    String? customerId,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // Create customer if not provided
      customerId ??= await createCustomer(
        email: customerEmail,
        name: customerName,
      );

      if (customerId == null) {
        return PaymentResult.error('Failed to create customer');
      }

      // Create payment intent
      final paymentIntentData = await _createPaymentIntent(
        amount: reservation.totalAmount,
        currency: 'USD',
        customerId: customerId,
        metadata: {
          'reservation_id': reservation.id ?? '',
          'user_id': reservation.userId,
          'room_type': reservation.roomType,
          'check_in': reservation.checkInDate.toIso8601String(),
          'check_out': reservation.checkOutDate.toIso8601String(),
        },
      );

      if (paymentIntentData == null) {
        return PaymentResult.error('Failed to create payment intent');
      }

      // Initialize payment sheet
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: paymentIntentData['client_secret'],
          merchantDisplayName: 'Hotel Reservation App',
          customerId: customerId,
          customerEphemeralKeySecret: await _createEphemeralKey(customerId),
          style: ThemeMode.system,
          billingDetails: BillingDetails(
            email: customerEmail,
            name: customerName,
          ),
        ),
      );

      // Present payment sheet
      await Stripe.instance.presentPaymentSheet();

      // Payment successful
      return PaymentResult.success(
        'Payment successful',
        paymentIntentData['id'],
        customerId,
      );
    } catch (e) {
      if (e is StripeException) {
        return PaymentResult.error(_getStripeErrorMessage(e));
      } else {
        return PaymentResult.error('Payment failed: $e');
      }
    }
  }

  // Create ephemeral key for customer
  Future<String?> _createEphemeralKey(String customerId) async {
    try {
      final response = await _dio.post(
        '$_stripeApiUrl/ephemeral_keys',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_stripeSecretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Stripe-Version': '2023-10-16',
          },
        ),
        data: {
          'customer': customerId,
        },
      );

      return response.data['secret'];
    } catch (e) {
      debugPrint('Error creating ephemeral key: $e');
      return null;
    }
  }

  // Refund payment
  Future<PaymentResult> refundPayment({
    required String paymentIntentId,
    double? amount, // If null, full refund
    String? reason,
  }) async {
    try {
      final response = await _dio.post(
        '$_stripeApiUrl/refunds',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_stripeSecretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: {
          'payment_intent': paymentIntentId,
          if (amount != null) 'amount': (amount * 100).round(),
          if (reason != null) 'reason': reason,
        },
      );

      return PaymentResult.success(
        'Refund processed successfully',
        response.data['id'],
        null,
      );
    } catch (e) {
      debugPrint('Error processing refund: $e');
      return PaymentResult.error('Refund failed: $e');
    }
  }

  // Get payment details
  Future<Map<String, dynamic>?> getPaymentDetails(String paymentIntentId) async {
    try {
      final response = await _dio.get(
        '$_stripeApiUrl/payment_intents/$paymentIntentId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_stripeSecretKey',
          },
        ),
      );

      return response.data;
    } catch (e) {
      debugPrint('Error getting payment details: $e');
      return null;
    }
  }

  // Get user-friendly error messages
  String _getStripeErrorMessage(StripeException error) {
    switch (error.error.code) {
      case FailureCode.Canceled:
        return 'Payment was cancelled';
      case FailureCode.Failed:
        return 'Payment failed. Please try again';
      case FailureCode.Timeout:
        return 'Payment timed out. Please try again';
      default:
        return error.error.localizedMessage ?? 'Payment failed. Please try again';
    }
  }
}

class PaymentResult {
  final bool isSuccess;
  final String message;
  final String? paymentIntentId;
  final String? customerId;

  PaymentResult._(this.isSuccess, this.message, this.paymentIntentId, this.customerId);

  factory PaymentResult.success(String message, String? paymentIntentId, String? customerId) =>
      PaymentResult._(true, message, paymentIntentId, customerId);
      
  factory PaymentResult.error(String message) =>
      PaymentResult._(false, message, null, null);
}
