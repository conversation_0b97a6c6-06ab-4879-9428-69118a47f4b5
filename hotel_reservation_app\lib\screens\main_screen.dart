import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../services/notification_service.dart';
import '../models/reservation_model.dart';
import '../utils/app_routes.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/custom_button.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final _formKey = GlobalKey<FormState>();

  // Room types and prices
  final Map<String, double> _roomTypes = {
    'Standard Room': 99.99,
    'Deluxe Room': 149.99,
    'Suite': 249.99,
    'Presidential Suite': 499.99,
  };

  // Form controllers and variables
  String? _selectedRoomType;
  DateTime? _checkInDate;
  DateTime? _checkOutDate;
  int _numberOfGuests = 1;
  final _specialRequestsController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _specialRequestsController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _selectCheckInDate() async {
    DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: DateTime.now(),
      maxTime: DateTime.now().add(const Duration(days: 365)),
      onConfirm: (date) {
        setState(() {
          _checkInDate = date;
          // Reset checkout date if it's before checkin
          if (_checkOutDate != null && _checkOutDate!.isBefore(date)) {
            _checkOutDate = null;
          }
        });
      },
      currentTime: _checkInDate ?? DateTime.now(),
      locale: LocaleType.en,
    );
  }

  Future<void> _selectCheckOutDate() async {
    if (_checkInDate == null) {
      _showSnackBar('Please select check-in date first', isError: true);
      return;
    }

    DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: _checkInDate!.add(const Duration(days: 1)),
      maxTime: DateTime.now().add(const Duration(days: 365)),
      onConfirm: (date) {
        setState(() {
          _checkOutDate = date;
        });
      },
      currentTime: _checkOutDate ?? _checkInDate!.add(const Duration(days: 1)),
      locale: LocaleType.en,
    );
  }

  double _calculateTotalAmount() {
    if (_selectedRoomType == null || _checkInDate == null || _checkOutDate == null) {
      return 0.0;
    }

    final roomPrice = _roomTypes[_selectedRoomType!]!;
    final numberOfNights = _checkOutDate!.difference(_checkInDate!).inDays;
    return roomPrice * numberOfNights;
  }

  Future<void> _bookRoom() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedRoomType == null) {
      _showSnackBar('Please select a room type', isError: true);
      return;
    }

    if (_checkInDate == null || _checkOutDate == null) {
      _showSnackBar('Please select check-in and check-out dates', isError: true);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final databaseService = Provider.of<DatabaseService>(context, listen: false);
      final notificationService = Provider.of<NotificationService>(context, listen: false);

      final user = authService.currentUser;
      if (user == null) {
        _showSnackBar('Please log in to make a booking', isError: true);
        setState(() => _isLoading = false);
        return;
      }

      final reservation = Reservation(
        userId: user.id!,
        roomType: _selectedRoomType!,
        checkInDate: _checkInDate!,
        checkOutDate: _checkOutDate!,
        guests: _numberOfGuests,
        totalAmount: _calculateTotalAmount(),
        createdAt: DateTime.now(),
      );

      final reservationId = await databaseService.createReservation(reservation);

      if (reservationId > 0) {
        reservation.id = reservationId;

        // Show booking confirmation notification
        await notificationService.showBookingConfirmation(
          guestName: user.name,
          roomType: _selectedRoomType!,
          checkInDate: _checkInDate!.toString().split(' ')[0],
          checkOutDate: _checkOutDate!.toString().split(' ')[0],
        );

        // Schedule check-in reminder
        final reminderDate = _checkInDate!.subtract(const Duration(days: 1));
        if (reminderDate.isAfter(DateTime.now())) {
          await notificationService.scheduleCheckInReminder(
            scheduledDate: reminderDate,
            roomType: _selectedRoomType!,
            checkInDate: _checkInDate!.toString().split(' ')[0],
          );
        }

        setState(() => _isLoading = false);
        _showSnackBar('Booking confirmed successfully!');

        // Navigate to report screen with booking data
        NavigationHelper.goToReport(context, {
          'reservation': reservation,
          'user': user,
          'specialRequests': _specialRequestsController.text,
        });
      } else {
        setState(() => _isLoading = false);
        _showSnackBar('Failed to create booking. Please try again.', isError: true);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      _showSnackBar('Error creating booking: $e', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Book a Room'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.hotel,
                      size: 48,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Find Your Perfect Stay',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Book your ideal room with us',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Room Type Selection
              Text(
                'Room Type',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: DropdownButtonFormField<String>(
                  value: _selectedRoomType,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  hint: const Text('Select room type'),
                  items: _roomTypes.entries.map((entry) {
                    return DropdownMenuItem<String>(
                      value: entry.key,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(entry.key),
                          Text(
                            '\$${entry.value.toStringAsFixed(2)}/night',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedRoomType = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Please select a room type';
                    }
                    return null;
                  },
                ),
              ),

              const SizedBox(height: 20),

              // Date Selection
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Check-in Date',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: _selectCheckInDate,
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Theme.of(context).colorScheme.outline),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  _checkInDate != null
                                      ? _checkInDate!.toString().split(' ')[0]
                                      : 'Select date',
                                  style: TextStyle(
                                    color: _checkInDate != null
                                        ? Theme.of(context).colorScheme.onSurface
                                        : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Check-out Date',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: _selectCheckOutDate,
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Theme.of(context).colorScheme.outline),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  _checkOutDate != null
                                      ? _checkOutDate!.toString().split(' ')[0]
                                      : 'Select date',
                                  style: TextStyle(
                                    color: _checkOutDate != null
                                        ? Theme.of(context).colorScheme.onSurface
                                        : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Number of Guests
              Text(
                'Number of Guests',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: _numberOfGuests > 1
                          ? () => setState(() => _numberOfGuests--)
                          : null,
                      icon: const Icon(Icons.remove),
                    ),
                    Expanded(
                      child: Text(
                        '$_numberOfGuests Guest${_numberOfGuests > 1 ? 's' : ''}',
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    IconButton(
                      onPressed: _numberOfGuests < 4
                          ? () => setState(() => _numberOfGuests++)
                          : null,
                      icon: const Icon(Icons.add),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Special Requests
              Text(
                'Special Requests (Optional)',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              CustomTextField(
                controller: _specialRequestsController,
                labelText: 'Special Requests',
                hintText: 'Any special requests or preferences...',
                maxLines: 3,
              ),

              const SizedBox(height: 24),

              // Booking Summary
              if (_selectedRoomType != null && _checkInDate != null && _checkOutDate != null)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Booking Summary',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildSummaryRow('Room Type', _selectedRoomType!),
                      _buildSummaryRow('Check-in', _checkInDate!.toString().split(' ')[0]),
                      _buildSummaryRow('Check-out', _checkOutDate!.toString().split(' ')[0]),
                      _buildSummaryRow('Guests', '$_numberOfGuests'),
                      _buildSummaryRow('Nights', '${_checkOutDate!.difference(_checkInDate!).inDays}'),
                      const Divider(),
                      _buildSummaryRow(
                        'Total Amount',
                        '\$${_calculateTotalAmount().toStringAsFixed(2)}',
                        isTotal: true,
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: 30),

              // Book Now Button
              CustomButton(
                text: 'Book Now',
                onPressed: _isLoading ? null : _bookRoom,
                isLoading: _isLoading,
                width: double.infinity,
                height: 56,
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Theme.of(context).colorScheme.primary : null,
            ),
          ),
        ],
      ),
    );
  }
}
