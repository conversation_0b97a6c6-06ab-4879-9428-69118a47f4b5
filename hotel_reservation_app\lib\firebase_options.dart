// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC7wwiMiRQOSFY-83sl-_CjmQfRy-2Fqdc',
    appId: '1:448618578101:web:0b650370bb29e29cac3efc',
    messagingSenderId: '448618578101',
    projectId: 'hotel-reservation-app-prod',
    authDomain: 'hotel-reservation-app-prod.firebaseapp.com',
    storageBucket: 'hotel-reservation-app-prod.appspot.com',
    measurementId: 'G-9922JQN1L2',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBdVl-cGnlqRlzSuJiEiStTEBJcKe7-4AA',
    appId: '1:448618578101:android:ac3efc0b650370bbac3efc',
    messagingSenderId: '448618578101',
    projectId: 'hotel-reservation-app-prod',
    storageBucket: 'hotel-reservation-app-prod.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBdVl-cGnlqRlzSuJiEiStTEBJcKe7-4AA',
    appId: '1:448618578101:ios:ac3efc0b650370bbac3efc',
    messagingSenderId: '448618578101',
    projectId: 'hotel-reservation-app-prod',
    storageBucket: 'hotel-reservation-app-prod.appspot.com',
    iosBundleId: 'com.hotelreservation.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBdVl-cGnlqRlzSuJiEiStTEBJcKe7-4AA',
    appId: '1:448618578101:macos:ac3efc0b650370bbac3efc',
    messagingSenderId: '448618578101',
    projectId: 'hotel-reservation-app-prod',
    storageBucket: 'hotel-reservation-app-prod.appspot.com',
    iosBundleId: 'com.hotelreservation.app',
  );
}
