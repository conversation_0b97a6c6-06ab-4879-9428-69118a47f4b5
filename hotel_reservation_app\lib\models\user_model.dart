class User {
  String? id; // Changed to String for Firestore
  String name;
  String email;
  String? phone;
  DateTime createdAt;
  bool isEmailVerified;
  String? profileImageUrl;
  DateTime? lastLoginAt;
  Map<String, dynamic>? preferences;

  User({
    this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.createdAt,
    this.isEmailVerified = false,
    this.profileImageUrl,
    this.lastLoginAt,
    this.preferences,
  });

  // Firestore serialization
  Map<String, dynamic> toFirestoreMap() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      'createdAt': createdAt,
      'isEmailVerified': isEmailVerified,
      'profileImageUrl': profileImageUrl,
      'lastLoginAt': lastLoginAt,
      'preferences': preferences ?? {},
    };
  }

  factory User.fromFirestoreMap(Map<String, dynamic> map, String documentId) {
    return User(
      id: documentId,
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'],
      createdAt: (map['createdAt'] as dynamic).toDate(),
      isEmailVerified: map['isEmailVerified'] ?? false,
      profileImageUrl: map['profileImageUrl'],
      lastLoginAt: map['lastLoginAt']?.toDate(),
      preferences: Map<String, dynamic>.from(map['preferences'] ?? {}),
    );
  }

  // Legacy SQLite support (for backward compatibility)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'created_at': createdAt.toIso8601String(),
      'is_email_verified': isEmailVerified ? 1 : 0,
      'profile_image_url': profileImageUrl,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id']?.toString(),
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'],
      createdAt: DateTime.parse(map['created_at']),
      isEmailVerified: (map['is_email_verified'] ?? 0) == 1,
      profileImageUrl: map['profile_image_url'],
    );
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    DateTime? createdAt,
    bool? isEmailVerified,
    String? profileImageUrl,
    DateTime? lastLoginAt,
    Map<String, dynamic>? preferences,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      createdAt: createdAt ?? this.createdAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, phone: $phone, createdAt: $createdAt, isEmailVerified: $isEmailVerified)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.phone == phone &&
        other.createdAt == createdAt &&
        other.isEmailVerified == isEmailVerified;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        createdAt.hashCode ^
        isEmailVerified.hashCode;
  }
}
