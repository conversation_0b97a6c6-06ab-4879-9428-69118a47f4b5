class Complaint {
  String? id; // Changed to String for Firestore
  String userId; // Changed to String for Firestore
  String? reservationId;
  String title;
  String description;
  String status;
  DateTime createdAt;
  String? category; // e.g., 'service', 'billing', 'facility'
  String? priority; // 'low', 'medium', 'high', 'urgent'
  DateTime? resolvedAt;
  String? resolution;
  String? assignedTo;

  Complaint({
    this.id,
    required this.userId,
    this.reservationId,
    required this.title,
    required this.description,
    this.status = 'pending',
    required this.createdAt,
    this.category,
    this.priority = 'medium',
    this.resolvedAt,
    this.resolution,
    this.assignedTo,
  });

  // Firestore serialization
  Map<String, dynamic> toFirestoreMap() {
    return {
      'userId': userId,
      'reservationId': reservationId,
      'title': title,
      'description': description,
      'status': status,
      'createdAt': createdAt,
      'category': category,
      'priority': priority,
      'resolvedAt': resolvedAt,
      'resolution': resolution,
      'assignedTo': assignedTo,
    };
  }

  factory Complaint.fromFirestoreMap(Map<String, dynamic> map, String documentId) {
    return Complaint(
      id: documentId,
      userId: map['userId'] ?? '',
      reservationId: map['reservationId'],
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      status: map['status'] ?? 'pending',
      createdAt: (map['createdAt'] as dynamic).toDate(),
      category: map['category'],
      priority: map['priority'] ?? 'medium',
      resolvedAt: map['resolvedAt']?.toDate(),
      resolution: map['resolution'],
      assignedTo: map['assignedTo'],
    );
  }

  // Legacy SQLite support
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'reservation_id': reservationId,
      'title': title,
      'description': description,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'category': category,
      'priority': priority,
    };
  }

  factory Complaint.fromMap(Map<String, dynamic> map) {
    return Complaint(
      id: map['id']?.toString(),
      userId: map['user_id']?.toString() ?? '',
      reservationId: map['reservation_id']?.toString(),
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      status: map['status'] ?? 'pending',
      createdAt: DateTime.parse(map['created_at']),
      category: map['category'],
      priority: map['priority'] ?? 'medium',
    );
  }

  Complaint copyWith({
    String? id,
    String? userId,
    String? reservationId,
    String? title,
    String? description,
    String? status,
    DateTime? createdAt,
    String? category,
    String? priority,
    DateTime? resolvedAt,
    String? resolution,
    String? assignedTo,
  }) {
    return Complaint(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      reservationId: reservationId ?? this.reservationId,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      resolution: resolution ?? this.resolution,
      assignedTo: assignedTo ?? this.assignedTo,
    );
  }

  @override
  String toString() {
    return 'Complaint(id: $id, userId: $userId, title: $title, description: $description, status: $status, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Complaint &&
        other.id == id &&
        other.userId == userId &&
        other.title == title &&
        other.description == description &&
        other.status == status &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        title.hashCode ^
        description.hashCode ^
        status.hashCode ^
        createdAt.hashCode;
  }
}
