class UserFeedback {
  String? id; // Changed to String for Firestore
  String userId; // Changed to String for Firestore
  String? reservationId;
  int rating;
  String? comment;
  DateTime createdAt;
  List<String>? categories; // e.g., ['service', 'cleanliness', 'location']
  bool isPublic;

  UserFeedback({
    this.id,
    required this.userId,
    this.reservationId,
    required this.rating,
    this.comment,
    required this.createdAt,
    this.categories,
    this.isPublic = true,
  });

  // Firestore serialization
  Map<String, dynamic> toFirestoreMap() {
    return {
      'userId': userId,
      'reservationId': reservationId,
      'rating': rating,
      'comment': comment,
      'createdAt': createdAt,
      'categories': categories ?? [],
      'isPublic': isPublic,
    };
  }

  factory UserFeedback.fromFirestoreMap(Map<String, dynamic> map, String documentId) {
    return UserFeedback(
      id: documentId,
      userId: map['userId'] ?? '',
      reservationId: map['reservationId'],
      rating: map['rating']?.toInt() ?? 0,
      comment: map['comment'],
      createdAt: (map['createdAt'] as dynamic).toDate(),
      categories: List<String>.from(map['categories'] ?? []),
      isPublic: map['isPublic'] ?? true,
    );
  }

  // Legacy SQLite support
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'reservation_id': reservationId,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
      'is_public': isPublic ? 1 : 0,
    };
  }

  factory UserFeedback.fromMap(Map<String, dynamic> map) {
    return UserFeedback(
      id: map['id']?.toString(),
      userId: map['user_id']?.toString() ?? '',
      reservationId: map['reservation_id']?.toString(),
      rating: map['rating']?.toInt() ?? 0,
      comment: map['comment'],
      createdAt: DateTime.parse(map['created_at']),
      isPublic: (map['is_public'] ?? 1) == 1,
    );
  }

  UserFeedback copyWith({
    String? id,
    String? userId,
    String? reservationId,
    int? rating,
    String? comment,
    DateTime? createdAt,
    List<String>? categories,
    bool? isPublic,
  }) {
    return UserFeedback(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      reservationId: reservationId ?? this.reservationId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      createdAt: createdAt ?? this.createdAt,
      categories: categories ?? this.categories,
      isPublic: isPublic ?? this.isPublic,
    );
  }

  @override
  String toString() {
    return 'UserFeedback(id: $id, userId: $userId, rating: $rating, comment: $comment, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserFeedback &&
        other.id == id &&
        other.userId == userId &&
        other.rating == rating &&
        other.comment == comment &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        rating.hashCode ^
        comment.hashCode ^
        createdAt.hashCode;
  }
}
