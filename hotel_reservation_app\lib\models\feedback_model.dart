class UserFeedback {
  int? id;
  int userId;
  int rating;
  String? comment;
  DateTime createdAt;

  UserFeedback({
    this.id,
    required this.userId,
    required this.rating,
    this.comment,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory UserFeedback.fromMap(Map<String, dynamic> map) {
    return UserFeedback(
      id: map['id']?.toInt(),
      userId: map['user_id']?.toInt() ?? 0,
      rating: map['rating']?.toInt() ?? 0,
      comment: map['comment'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  UserFeedback copyWith({
    int? id,
    int? userId,
    int? rating,
    String? comment,
    DateTime? createdAt,
  }) {
    return UserFeedback(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'UserFeedback(id: $id, userId: $userId, rating: $rating, comment: $comment, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserFeedback &&
        other.id == id &&
        other.userId == userId &&
        other.rating == rating &&
        other.comment == comment &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        rating.hashCode ^
        comment.hashCode ^
        createdAt.hashCode;
  }
}
