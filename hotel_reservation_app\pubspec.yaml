name: hotel_reservation_app
description: "A comprehensive hotel reservation mobile app with SQLite database, authentication, and booking features."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Database
  sqflite: ^2.3.0
  path: ^1.8.3

  # HTTP and connectivity
  http: ^1.1.0
  connectivity_plus: ^5.0.2

  # State management and navigation
  provider: ^6.1.1
  go_router: ^12.1.3

  # UI components
  flutter_launcher_icons: ^0.13.1
  shared_preferences: ^2.2.2

  # Date and time pickers
  flutter_datetime_picker_plus: ^2.1.0

  # Notifications
  flutter_local_notifications: ^16.3.2

  # Form validation
  email_validator: ^2.1.17

  # Icons and fonts
  font_awesome_flutter: ^10.6.0

  # Firebase - Production Backend
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.8

  # Payment Processing
  stripe_payment: ^1.1.4
  flutter_stripe: ^9.5.0

  # Real-time Communication
  socket_io_client: ^2.0.3+1

  # Maps and Location
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  geocoding: ^2.1.1

  # Image Processing
  image_picker: ^1.0.4
  cached_network_image: ^3.3.0

  # Advanced HTTP
  dio: ^5.3.2

  # Security
  crypto: ^3.0.3
  encrypt: ^5.0.1

  # Push Notifications
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^16.3.2

  # Social Auth
  google_sign_in: ^6.1.6
  sign_in_with_apple: ^5.0.0

  # Email/SMS Services
  mailer: ^6.0.1

  # State Management (Advanced)
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # Utilities
  uuid: ^4.2.1
  intl: ^0.19.0
  url_launcher: ^6.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
