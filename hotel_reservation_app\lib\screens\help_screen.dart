import 'package:flutter/material.dart';
import '../utils/app_routes.dart';

class HelpScreen extends StatefulWidget {
  const HelpScreen({super.key});

  @override
  State<HelpScreen> createState() => _HelpScreenState();
}

class _HelpScreenState extends State<HelpScreen> {
  final List<FAQItem> _faqItems = [
    FAQItem(
      question: 'How do I make a reservation?',
      answer: 'To make a reservation, go to the "Book a Room" section from the main menu. Select your preferred room type, check-in and check-out dates, number of guests, and any special requests. Then tap "Book Now" to confirm your reservation.',
    ),
    FAQItem(
      question: 'Can I cancel or modify my booking?',
      answer: 'Yes, you can view and manage your bookings in the "My Bookings" section. Please note that cancellation policies may apply depending on the room type and booking date.',
    ),
    FAQItem(
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards including Visa, MasterCard, American Express, and Discover. We also accept PayPal and bank transfers for certain bookings.',
    ),
    FAQItem(
      question: 'How do I reset my password?',
      answer: 'On the login screen, tap "Forgot Password?" and enter your email address. We\'ll send you instructions to reset your password.',
    ),
    FAQItem(
      question: 'What amenities are included?',
      answer: 'Amenities vary by room type but typically include free Wi-Fi, air conditioning, private bathroom, TV, and daily housekeeping. Premium rooms may include additional amenities like mini-bar, balcony, or spa access.',
    ),
    FAQItem(
      question: 'What is your check-in/check-out policy?',
      answer: 'Standard check-in time is 3:00 PM and check-out time is 11:00 AM. Early check-in and late check-out may be available upon request and subject to availability.',
    ),
    FAQItem(
      question: 'How do I contact customer support?',
      answer: 'You can contact our customer support team through the "Contact" section in the app, or call our 24/7 helpline. We\'re here to help with any questions or concerns.',
    ),
    FAQItem(
      question: 'Is my personal information secure?',
      answer: 'Yes, we take your privacy and security seriously. All personal information is encrypted and stored securely. Please review our Privacy Policy for more details.',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.help_center,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'How can we help you?',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Find answers to common questions or get in touch with our support team',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Quick Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quick Actions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    _buildQuickActionTile(
                      'Contact Support',
                      'Get in touch with our support team',
                      Icons.support_agent,
                      () => NavigationHelper.goToContact(context),
                    ),
                    const Divider(),
                    _buildQuickActionTile(
                      'Submit Feedback',
                      'Share your experience with us',
                      Icons.feedback,
                      () => NavigationHelper.goToUserFeedback(context),
                    ),
                    const Divider(),
                    _buildQuickActionTile(
                      'Report an Issue',
                      'Let us know about any problems',
                      Icons.report_problem,
                      () => NavigationHelper.goToComplains(context),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // FAQ Section
            Text(
              'Frequently Asked Questions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // FAQ List
            ...(_faqItems.map((faq) => _buildFAQCard(faq)).toList()),

            const SizedBox(height: 24),

            // Additional Help
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Icon(
                      Icons.phone_in_talk,
                      size: 48,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Still need help?',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Our customer support team is available 24/7 to assist you with any questions or concerns.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => NavigationHelper.goToContact(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      ),
                      child: const Text('Contact Support'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildFAQCard(FAQItem faq) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        title: Text(
          faq.question,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              faq.answer,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class FAQItem {
  final String question;
  final String answer;

  FAQItem({
    required this.question,
    required this.answer,
  });
}
