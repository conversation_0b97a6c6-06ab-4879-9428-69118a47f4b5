import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/reservation_model.dart';

class EmailService {
  static final EmailService _instance = EmailService._internal();
  static EmailService get instance => _instance;
  EmailService._internal();

  final Dio _dio = Dio();
  
  // SendGrid API configuration
  static const String _sendGridApiKey = 'SG.your-sendgrid-api-key-here';
  static const String _sendGridUrl = 'https://api.sendgrid.com/v3/mail/send';
  static const String _fromEmail = '<EMAIL>';
  static const String _fromName = 'Hotel Reservation App';

  // Email templates
  static const String _companyName = 'Hotel Reservation App';
  static const String _supportEmail = '<EMAIL>';
  static const String _websiteUrl = 'https://hotelreservation.com';

  Future<bool> _sendEmail({
    required String toEmail,
    required String toName,
    required String subject,
    required String htmlContent,
    String? textContent,
  }) async {
    try {
      final response = await _dio.post(
        _sendGridUrl,
        options: Options(
          headers: {
            'Authorization': 'Bearer $_sendGridApiKey',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'personalizations': [
            {
              'to': [
                {
                  'email': toEmail,
                  'name': toName,
                }
              ],
              'subject': subject,
            }
          ],
          'from': {
            'email': _fromEmail,
            'name': _fromName,
          },
          'content': [
            {
              'type': 'text/html',
              'value': htmlContent,
            },
            if (textContent != null)
              {
                'type': 'text/plain',
                'value': textContent,
              },
          ],
        },
      );

      return response.statusCode == 202;
    } catch (e) {
      debugPrint('Error sending email: $e');
      return false;
    }
  }

  // Welcome email for new users
  Future<bool> sendWelcomeEmail(String email, String name) async {
    final subject = 'Welcome to $_companyName!';
    final htmlContent = '''
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2E7D32; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { background: #2E7D32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Welcome to $_companyName!</h1>
            </div>
            <div class="content">
                <h2>Hello $name,</h2>
                <p>Thank you for joining $_companyName! We're excited to help you find the perfect accommodation for your travels.</p>
                
                <p>With our app, you can:</p>
                <ul>
                    <li>Browse and book from thousands of hotels worldwide</li>
                    <li>Get instant booking confirmations</li>
                    <li>Manage your reservations easily</li>
                    <li>Access 24/7 customer support</li>
                </ul>
                
                <p>To get started, please verify your email address by clicking the verification link sent to your email.</p>
                
                <a href="$_websiteUrl" class="button">Start Exploring</a>
                
                <p>If you have any questions, feel free to contact our support team at $_supportEmail.</p>
                
                <p>Happy travels!</p>
                <p>The $_companyName Team</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 $_companyName. All rights reserved.</p>
                <p>Visit us at <a href="$_websiteUrl">$_websiteUrl</a></p>
            </div>
        </div>
    </body>
    </html>
    ''';

    return await _sendEmail(
      toEmail: email,
      toName: name,
      subject: subject,
      htmlContent: htmlContent,
    );
  }

  // Password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    final subject = 'Password Reset - $_companyName';
    final htmlContent = '''
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #FF6F00; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { background: #FF6F00; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Password Reset Request</h1>
            </div>
            <div class="content">
                <h2>Hello,</h2>
                <p>We received a request to reset your password for your $_companyName account.</p>
                
                <p>If you requested this password reset, please click the button below to reset your password:</p>
                
                <a href="$_websiteUrl/reset-password" class="button">Reset Password</a>
                
                <div class="warning">
                    <strong>Security Notice:</strong>
                    <ul>
                        <li>This link will expire in 24 hours</li>
                        <li>If you didn't request this reset, please ignore this email</li>
                        <li>Never share your password with anyone</li>
                    </ul>
                </div>
                
                <p>If you're having trouble with the button above, copy and paste the following link into your browser:</p>
                <p>$_websiteUrl/reset-password</p>
                
                <p>If you didn't request this password reset, please contact our support team immediately at $_supportEmail.</p>
                
                <p>Best regards,</p>
                <p>The $_companyName Security Team</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 $_companyName. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    ''';

    return await _sendEmail(
      toEmail: email,
      toName: 'User',
      subject: subject,
      htmlContent: htmlContent,
    );
  }

  // Booking confirmation email
  Future<bool> sendBookingConfirmationEmail({
    required String email,
    required String name,
    required Reservation reservation,
    required String hotelName,
    required String hotelAddress,
  }) async {
    final subject = 'Booking Confirmed - $_companyName';
    final checkInDate = '${reservation.checkInDate.day}/${reservation.checkInDate.month}/${reservation.checkInDate.year}';
    final checkOutDate = '${reservation.checkOutDate.day}/${reservation.checkOutDate.month}/${reservation.checkOutDate.year}';
    
    final htmlContent = '''
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2E7D32; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .booking-details { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee; }
            .total { background: #e8f5e8; padding: 10px; border-radius: 5px; font-weight: bold; }
            .button { background: #2E7D32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎉 Booking Confirmed!</h1>
                <p>Confirmation #${reservation.id}</p>
            </div>
            <div class="content">
                <h2>Hello $name,</h2>
                <p>Great news! Your hotel reservation has been confirmed. Here are your booking details:</p>
                
                <div class="booking-details">
                    <h3>Hotel Information</h3>
                    <div class="detail-row">
                        <span>Hotel:</span>
                        <span>$hotelName</span>
                    </div>
                    <div class="detail-row">
                        <span>Address:</span>
                        <span>$hotelAddress</span>
                    </div>
                    
                    <h3>Booking Details</h3>
                    <div class="detail-row">
                        <span>Room Type:</span>
                        <span>${reservation.roomType}</span>
                    </div>
                    <div class="detail-row">
                        <span>Check-in:</span>
                        <span>$checkInDate</span>
                    </div>
                    <div class="detail-row">
                        <span>Check-out:</span>
                        <span>$checkOutDate</span>
                    </div>
                    <div class="detail-row">
                        <span>Guests:</span>
                        <span>${reservation.guests}</span>
                    </div>
                    <div class="detail-row">
                        <span>Nights:</span>
                        <span>${reservation.numberOfNights}</span>
                    </div>
                    
                    <div class="total">
                        <div class="detail-row">
                            <span>Total Amount:</span>
                            <span>\$${reservation.totalAmount.toStringAsFixed(2)}</span>
                        </div>
                    </div>
                </div>
                
                <h3>Important Information:</h3>
                <ul>
                    <li>Check-in time: 3:00 PM</li>
                    <li>Check-out time: 11:00 AM</li>
                    <li>Please bring a valid ID for check-in</li>
                    <li>Cancellation policy applies</li>
                </ul>
                
                <a href="$_websiteUrl/booking/${reservation.id}" class="button">View Booking Details</a>
                
                <p>If you need to make any changes or have questions, please contact us at $_supportEmail or call our 24/7 support line.</p>
                
                <p>We look forward to hosting you!</p>
                <p>The $_companyName Team</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 $_companyName. All rights reserved.</p>
                <p>Need help? Contact us at $_supportEmail</p>
            </div>
        </div>
    </body>
    </html>
    ''';

    return await _sendEmail(
      toEmail: email,
      toName: name,
      subject: subject,
      htmlContent: htmlContent,
    );
  }

  // Check-in reminder email
  Future<bool> sendCheckInReminderEmail({
    required String email,
    required String name,
    required Reservation reservation,
    required String hotelName,
  }) async {
    final subject = 'Check-in Reminder - $_companyName';
    final checkInDate = '${reservation.checkInDate.day}/${reservation.checkInDate.month}/${reservation.checkInDate.year}';
    
    final htmlContent = '''
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #FF6F00; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .reminder-box { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .button { background: #FF6F00; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>⏰ Check-in Reminder</h1>
            </div>
            <div class="content">
                <h2>Hello $name,</h2>
                <p>This is a friendly reminder that your check-in is tomorrow!</p>
                
                <div class="reminder-box">
                    <h3>Your Booking Details:</h3>
                    <p><strong>Hotel:</strong> $hotelName</p>
                    <p><strong>Check-in Date:</strong> $checkInDate</p>
                    <p><strong>Room Type:</strong> ${reservation.roomType}</p>
                    <p><strong>Confirmation #:</strong> ${reservation.id}</p>
                </div>
                
                <h3>Check-in Information:</h3>
                <ul>
                    <li>Check-in time: 3:00 PM</li>
                    <li>Please bring a valid photo ID</li>
                    <li>Early check-in may be available upon request</li>
                </ul>
                
                <a href="$_websiteUrl/booking/${reservation.id}" class="button">View Full Details</a>
                
                <p>We're excited to welcome you! If you have any questions or special requests, please don't hesitate to contact us.</p>
                
                <p>Safe travels!</p>
                <p>The $_companyName Team</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 $_companyName. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    ''';

    return await _sendEmail(
      toEmail: email,
      toName: name,
      subject: subject,
      htmlContent: htmlContent,
    );
  }
}
