class Hotel {
  String? id;
  String name;
  String description;
  String address;
  String city;
  String country;
  double latitude;
  double longitude;
  List<String> imageUrls;
  List<String> amenities;
  Map<String, RoomType> roomTypes;
  double rating;
  int reviewCount;
  String phone;
  String email;
  String website;
  bool isActive;
  DateTime createdAt;
  DateTime updatedAt;

  Hotel({
    this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.city,
    required this.country,
    required this.latitude,
    required this.longitude,
    required this.imageUrls,
    required this.amenities,
    required this.roomTypes,
    this.rating = 0.0,
    this.reviewCount = 0,
    required this.phone,
    required this.email,
    required this.website,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toFirestoreMap() {
    return {
      'name': name,
      'description': description,
      'address': address,
      'city': city,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
      'imageUrls': imageUrls,
      'amenities': amenities,
      'roomTypes': roomTypes.map((key, value) => MapEntry(key, value.toMap())),
      'rating': rating,
      'reviewCount': reviewCount,
      'phone': phone,
      'email': email,
      'website': website,
      'isActive': isActive,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  factory Hotel.fromFirestoreMap(Map<String, dynamic> map, String documentId) {
    return Hotel(
      id: documentId,
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      address: map['address'] ?? '',
      city: map['city'] ?? '',
      country: map['country'] ?? '',
      latitude: map['latitude']?.toDouble() ?? 0.0,
      longitude: map['longitude']?.toDouble() ?? 0.0,
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      amenities: List<String>.from(map['amenities'] ?? []),
      roomTypes: (map['roomTypes'] as Map<String, dynamic>? ?? {})
          .map((key, value) => MapEntry(key, RoomType.fromMap(value))),
      rating: map['rating']?.toDouble() ?? 0.0,
      reviewCount: map['reviewCount']?.toInt() ?? 0,
      phone: map['phone'] ?? '',
      email: map['email'] ?? '',
      website: map['website'] ?? '',
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as dynamic).toDate(),
      updatedAt: (map['updatedAt'] as dynamic).toDate(),
    );
  }

  Hotel copyWith({
    String? id,
    String? name,
    String? description,
    String? address,
    String? city,
    String? country,
    double? latitude,
    double? longitude,
    List<String>? imageUrls,
    List<String>? amenities,
    Map<String, RoomType>? roomTypes,
    double? rating,
    int? reviewCount,
    String? phone,
    String? email,
    String? website,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Hotel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      imageUrls: imageUrls ?? this.imageUrls,
      amenities: amenities ?? this.amenities,
      roomTypes: roomTypes ?? this.roomTypes,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class RoomType {
  String name;
  String description;
  double pricePerNight;
  int maxGuests;
  double size; // in square meters
  List<String> amenities;
  List<String> imageUrls;
  int totalRooms;
  bool isAvailable;

  RoomType({
    required this.name,
    required this.description,
    required this.pricePerNight,
    required this.maxGuests,
    required this.size,
    required this.amenities,
    required this.imageUrls,
    required this.totalRooms,
    this.isAvailable = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'pricePerNight': pricePerNight,
      'maxGuests': maxGuests,
      'size': size,
      'amenities': amenities,
      'imageUrls': imageUrls,
      'totalRooms': totalRooms,
      'isAvailable': isAvailable,
    };
  }

  factory RoomType.fromMap(Map<String, dynamic> map) {
    return RoomType(
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      pricePerNight: map['pricePerNight']?.toDouble() ?? 0.0,
      maxGuests: map['maxGuests']?.toInt() ?? 1,
      size: map['size']?.toDouble() ?? 0.0,
      amenities: List<String>.from(map['amenities'] ?? []),
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      totalRooms: map['totalRooms']?.toInt() ?? 0,
      isAvailable: map['isAvailable'] ?? true,
    );
  }

  RoomType copyWith({
    String? name,
    String? description,
    double? pricePerNight,
    int? maxGuests,
    double? size,
    List<String>? amenities,
    List<String>? imageUrls,
    int? totalRooms,
    bool? isAvailable,
  }) {
    return RoomType(
      name: name ?? this.name,
      description: description ?? this.description,
      pricePerNight: pricePerNight ?? this.pricePerNight,
      maxGuests: maxGuests ?? this.maxGuests,
      size: size ?? this.size,
      amenities: amenities ?? this.amenities,
      imageUrls: imageUrls ?? this.imageUrls,
      totalRooms: totalRooms ?? this.totalRooms,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }
}
