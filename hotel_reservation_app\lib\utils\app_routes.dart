import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';

// Import all screens
import '../screens/splash_screen.dart';
import '../screens/login_screen.dart';
import '../screens/signup_screen.dart';
import '../screens/reset_password_screen.dart';
import '../screens/dashboard_screen.dart';
import '../screens/main_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/account_screen.dart';
import '../screens/user_feedback_screen.dart';
import '../screens/complains_screen.dart';
import '../screens/help_screen.dart';
import '../screens/contact_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../screens/report_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String resetPassword = '/reset-password';
  static const String dashboard = '/dashboard';
  static const String main = '/main';
  static const String settings = '/settings';
  static const String account = '/account';
  static const String userFeedback = '/user-feedback';
  static const String complains = '/complains';
  static const String help = '/help';
  static const String contact = '/contact';
  static const String privacyPolicy = '/privacy-policy';
  static const String report = '/report';

  static final GoRouter router = GoRouter(
    initialLocation: splash,
    routes: [
      // Splash Screen
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Authentication Routes
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: signup,
        name: 'signup',
        builder: (context, state) => const SignUpScreen(),
      ),
      GoRoute(
        path: resetPassword,
        name: 'reset-password',
        builder: (context, state) => const ResetPasswordScreen(),
      ),
      
      // Main App Routes
      GoRoute(
        path: dashboard,
        name: 'dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),
      GoRoute(
        path: main,
        name: 'main',
        builder: (context, state) => const MainScreen(),
      ),
      
      // Settings and Account Routes
      GoRoute(
        path: settings,
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),
      GoRoute(
        path: account,
        name: 'account',
        builder: (context, state) => const AccountScreen(),
      ),
      
      // Support Routes
      GoRoute(
        path: userFeedback,
        name: 'user-feedback',
        builder: (context, state) => const UserFeedbackScreen(),
      ),
      GoRoute(
        path: complains,
        name: 'complains',
        builder: (context, state) => const ComplainsScreen(),
      ),
      GoRoute(
        path: help,
        name: 'help',
        builder: (context, state) => const HelpScreen(),
      ),
      GoRoute(
        path: contact,
        name: 'contact',
        builder: (context, state) => const ContactScreen(),
      ),
      GoRoute(
        path: privacyPolicy,
        name: 'privacy-policy',
        builder: (context, state) => const PrivacyPolicyScreen(),
      ),
      
      // Report Route
      GoRoute(
        path: report,
        name: 'report',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          return ReportScreen(bookingData: extra);
        },
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found: ${state.location}',
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(splash),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}

// Navigation helper class
class NavigationHelper {
  static void goToLogin(BuildContext context) {
    context.go(AppRoutes.login);
  }
  
  static void goToSignUp(BuildContext context) {
    context.go(AppRoutes.signup);
  }
  
  static void goToResetPassword(BuildContext context) {
    context.go(AppRoutes.resetPassword);
  }
  
  static void goToDashboard(BuildContext context) {
    context.go(AppRoutes.dashboard);
  }
  
  static void goToMain(BuildContext context) {
    context.go(AppRoutes.main);
  }
  
  static void goToSettings(BuildContext context) {
    context.go(AppRoutes.settings);
  }
  
  static void goToAccount(BuildContext context) {
    context.go(AppRoutes.account);
  }
  
  static void goToUserFeedback(BuildContext context) {
    context.go(AppRoutes.userFeedback);
  }
  
  static void goToComplains(BuildContext context) {
    context.go(AppRoutes.complains);
  }
  
  static void goToHelp(BuildContext context) {
    context.go(AppRoutes.help);
  }
  
  static void goToContact(BuildContext context) {
    context.go(AppRoutes.contact);
  }
  
  static void goToPrivacyPolicy(BuildContext context) {
    context.go(AppRoutes.privacyPolicy);
  }
  
  static void goToReport(BuildContext context, Map<String, dynamic>? bookingData) {
    context.go(AppRoutes.report, extra: bookingData);
  }
  
  static void logout(BuildContext context) {
    context.go(AppRoutes.login);
  }
}
