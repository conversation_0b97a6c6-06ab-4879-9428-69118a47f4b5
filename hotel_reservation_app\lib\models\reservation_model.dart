class Reservation {
  String? id; // Changed to String for Firestore
  String userId; // Changed to String for Firestore
  String? hotelId;
  String roomType;
  DateTime checkInDate;
  DateTime checkOutDate;
  int guests;
  double totalAmount;
  String status;
  DateTime createdAt;
  String? specialRequests;
  Map<String, dynamic>? paymentDetails;
  DateTime? cancelledAt;

  Reservation({
    this.id,
    required this.userId,
    this.hotelId,
    required this.roomType,
    required this.checkInDate,
    required this.checkOutDate,
    required this.guests,
    required this.totalAmount,
    this.status = 'confirmed',
    required this.createdAt,
    this.specialRequests,
    this.paymentDetails,
    this.cancelledAt,
  });

  // Firestore serialization
  Map<String, dynamic> toFirestoreMap() {
    return {
      'userId': userId,
      'hotelId': hotelId,
      'roomType': roomType,
      'checkInDate': checkInDate,
      'checkOutDate': checkOutDate,
      'guests': guests,
      'totalAmount': totalAmount,
      'status': status,
      'createdAt': createdAt,
      'specialRequests': specialRequests,
      'paymentDetails': paymentDetails ?? {},
      'cancelledAt': cancelledAt,
    };
  }

  factory Reservation.fromFirestoreMap(Map<String, dynamic> map, String documentId) {
    return Reservation(
      id: documentId,
      userId: map['userId'] ?? '',
      hotelId: map['hotelId'],
      roomType: map['roomType'] ?? '',
      checkInDate: (map['checkInDate'] as dynamic).toDate(),
      checkOutDate: (map['checkOutDate'] as dynamic).toDate(),
      guests: map['guests']?.toInt() ?? 1,
      totalAmount: map['totalAmount']?.toDouble() ?? 0.0,
      status: map['status'] ?? 'confirmed',
      createdAt: (map['createdAt'] as dynamic).toDate(),
      specialRequests: map['specialRequests'],
      paymentDetails: Map<String, dynamic>.from(map['paymentDetails'] ?? {}),
      cancelledAt: map['cancelledAt']?.toDate(),
    );
  }

  // Legacy SQLite support
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'hotel_id': hotelId,
      'room_type': roomType,
      'check_in_date': checkInDate.toIso8601String(),
      'check_out_date': checkOutDate.toIso8601String(),
      'guests': guests,
      'total_amount': totalAmount,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'special_requests': specialRequests,
    };
  }

  factory Reservation.fromMap(Map<String, dynamic> map) {
    return Reservation(
      id: map['id']?.toString(),
      userId: map['user_id']?.toString() ?? '',
      hotelId: map['hotel_id']?.toString(),
      roomType: map['room_type'] ?? '',
      checkInDate: DateTime.parse(map['check_in_date']),
      checkOutDate: DateTime.parse(map['check_out_date']),
      guests: map['guests']?.toInt() ?? 1,
      totalAmount: map['total_amount']?.toDouble() ?? 0.0,
      status: map['status'] ?? 'confirmed',
      createdAt: DateTime.parse(map['created_at']),
      specialRequests: map['special_requests'],
    );
  }

  Reservation copyWith({
    String? id,
    String? userId,
    String? hotelId,
    String? roomType,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? guests,
    double? totalAmount,
    String? status,
    DateTime? createdAt,
    String? specialRequests,
    Map<String, dynamic>? paymentDetails,
    DateTime? cancelledAt,
  }) {
    return Reservation(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      hotelId: hotelId ?? this.hotelId,
      roomType: roomType ?? this.roomType,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      guests: guests ?? this.guests,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      specialRequests: specialRequests ?? this.specialRequests,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      cancelledAt: cancelledAt ?? this.cancelledAt,
    );
  }

  int get numberOfNights {
    return checkOutDate.difference(checkInDate).inDays;
  }

  @override
  String toString() {
    return 'Reservation(id: $id, userId: $userId, roomType: $roomType, checkInDate: $checkInDate, checkOutDate: $checkOutDate, guests: $guests, totalAmount: $totalAmount, status: $status, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Reservation &&
        other.id == id &&
        other.userId == userId &&
        other.roomType == roomType &&
        other.checkInDate == checkInDate &&
        other.checkOutDate == checkOutDate &&
        other.guests == guests &&
        other.totalAmount == totalAmount &&
        other.status == status &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        roomType.hashCode ^
        checkInDate.hashCode ^
        checkOutDate.hashCode ^
        guests.hashCode ^
        totalAmount.hashCode ^
        status.hashCode ^
        createdAt.hashCode;
  }
}
