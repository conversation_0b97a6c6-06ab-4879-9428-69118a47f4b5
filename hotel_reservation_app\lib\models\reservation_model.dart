class Reservation {
  int? id;
  int userId;
  String roomType;
  DateTime checkInDate;
  DateTime checkOutDate;
  int guests;
  double totalAmount;
  String status;
  DateTime createdAt;

  Reservation({
    this.id,
    required this.userId,
    required this.roomType,
    required this.checkInDate,
    required this.checkOutDate,
    required this.guests,
    required this.totalAmount,
    this.status = 'confirmed',
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'room_type': roomType,
      'check_in_date': checkInDate.toIso8601String(),
      'check_out_date': checkOutDate.toIso8601String(),
      'guests': guests,
      'total_amount': totalAmount,
      'status': status,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory Reservation.fromMap(Map<String, dynamic> map) {
    return Reservation(
      id: map['id']?.toInt(),
      userId: map['user_id']?.toInt() ?? 0,
      roomType: map['room_type'] ?? '',
      checkInDate: DateTime.parse(map['check_in_date']),
      checkOutDate: DateTime.parse(map['check_out_date']),
      guests: map['guests']?.toInt() ?? 1,
      totalAmount: map['total_amount']?.toDouble() ?? 0.0,
      status: map['status'] ?? 'confirmed',
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  Reservation copyWith({
    int? id,
    int? userId,
    String? roomType,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? guests,
    double? totalAmount,
    String? status,
    DateTime? createdAt,
  }) {
    return Reservation(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      roomType: roomType ?? this.roomType,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      guests: guests ?? this.guests,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  int get numberOfNights {
    return checkOutDate.difference(checkInDate).inDays;
  }

  @override
  String toString() {
    return 'Reservation(id: $id, userId: $userId, roomType: $roomType, checkInDate: $checkInDate, checkOutDate: $checkOutDate, guests: $guests, totalAmount: $totalAmount, status: $status, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Reservation &&
        other.id == id &&
        other.userId == userId &&
        other.roomType == roomType &&
        other.checkInDate == checkInDate &&
        other.checkOutDate == checkOutDate &&
        other.guests == guests &&
        other.totalAmount == totalAmount &&
        other.status == status &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        roomType.hashCode ^
        checkInDate.hashCode ^
        checkOutDate.hashCode ^
        guests.hashCode ^
        totalAmount.hashCode ^
        status.hashCode ^
        createdAt.hashCode;
  }
}
