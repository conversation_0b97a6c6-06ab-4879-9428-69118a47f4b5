import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart' as app_models;
import '../models/reservation_model.dart';
import '../models/feedback_model.dart';
import '../models/complaint_model.dart';
import '../models/hotel_model.dart';

class FirestoreService {
  static final FirestoreService _instance = FirestoreService._internal();
  static FirestoreService get instance => _instance;
  FirestoreService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection references
  CollectionReference get _usersCollection => _firestore.collection('users');
  CollectionReference get _reservationsCollection => _firestore.collection('reservations');
  CollectionReference get _feedbacksCollection => _firestore.collection('feedbacks');
  CollectionReference get _complaintsCollection => _firestore.collection('complaints');
  CollectionReference get _hotelsCollection => _firestore.collection('hotels');
  CollectionReference get _roomsCollection => _firestore.collection('rooms');

  // User operations
  Future<void> createUser(app_models.User user) async {
    try {
      await _usersCollection.doc(user.id).set(user.toFirestoreMap());
    } catch (e) {
      debugPrint('Error creating user: $e');
      rethrow;
    }
  }

  Future<app_models.User?> getUser(String uid) async {
    try {
      final doc = await _usersCollection.doc(uid).get();
      if (doc.exists) {
        return app_models.User.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user: $e');
      return null;
    }
  }

  Future<void> updateUser(app_models.User user) async {
    try {
      await _usersCollection.doc(user.id).update(user.toFirestoreMap());
    } catch (e) {
      debugPrint('Error updating user: $e');
      rethrow;
    }
  }

  Future<void> updateUserEmailVerification(String uid, bool isVerified) async {
    try {
      await _usersCollection.doc(uid).update({'isEmailVerified': isVerified});
    } catch (e) {
      debugPrint('Error updating email verification: $e');
      rethrow;
    }
  }

  Future<void> deleteUser(String uid) async {
    try {
      // Delete user document
      await _usersCollection.doc(uid).delete();
      
      // Delete user's reservations
      final reservations = await _reservationsCollection
          .where('userId', isEqualTo: uid)
          .get();
      
      for (final doc in reservations.docs) {
        await doc.reference.delete();
      }
      
      // Delete user's feedbacks
      final feedbacks = await _feedbacksCollection
          .where('userId', isEqualTo: uid)
          .get();
      
      for (final doc in feedbacks.docs) {
        await doc.reference.delete();
      }
      
      // Delete user's complaints
      final complaints = await _complaintsCollection
          .where('userId', isEqualTo: uid)
          .get();
      
      for (final doc in complaints.docs) {
        await doc.reference.delete();
      }
    } catch (e) {
      debugPrint('Error deleting user: $e');
      rethrow;
    }
  }

  // Reservation operations
  Future<String> createReservation(Reservation reservation) async {
    try {
      final docRef = await _reservationsCollection.add(reservation.toFirestoreMap());
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating reservation: $e');
      rethrow;
    }
  }

  Future<List<Reservation>> getUserReservations(String userId) async {
    try {
      final querySnapshot = await _reservationsCollection
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Reservation.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error getting user reservations: $e');
      return [];
    }
  }

  Future<Reservation?> getReservation(String reservationId) async {
    try {
      final doc = await _reservationsCollection.doc(reservationId).get();
      if (doc.exists) {
        return Reservation.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting reservation: $e');
      return null;
    }
  }

  Future<void> updateReservation(Reservation reservation) async {
    try {
      await _reservationsCollection.doc(reservation.id).update(reservation.toFirestoreMap());
    } catch (e) {
      debugPrint('Error updating reservation: $e');
      rethrow;
    }
  }

  Future<void> cancelReservation(String reservationId) async {
    try {
      await _reservationsCollection.doc(reservationId).update({
        'status': 'cancelled',
        'cancelledAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error cancelling reservation: $e');
      rethrow;
    }
  }

  // Feedback operations
  Future<String> createFeedback(UserFeedback feedback) async {
    try {
      final docRef = await _feedbacksCollection.add(feedback.toFirestoreMap());
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating feedback: $e');
      rethrow;
    }
  }

  Future<List<UserFeedback>> getUserFeedbacks(String userId) async {
    try {
      final querySnapshot = await _feedbacksCollection
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => UserFeedback.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error getting user feedbacks: $e');
      return [];
    }
  }

  // Complaint operations
  Future<String> createComplaint(Complaint complaint) async {
    try {
      final docRef = await _complaintsCollection.add(complaint.toFirestoreMap());
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating complaint: $e');
      rethrow;
    }
  }

  Future<List<Complaint>> getUserComplaints(String userId) async {
    try {
      final querySnapshot = await _complaintsCollection
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Complaint.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error getting user complaints: $e');
      return [];
    }
  }

  // Hotel operations
  Future<List<Hotel>> getHotels() async {
    try {
      final querySnapshot = await _hotelsCollection
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Hotel.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error getting hotels: $e');
      return [];
    }
  }

  Future<Hotel?> getHotel(String hotelId) async {
    try {
      final doc = await _hotelsCollection.doc(hotelId).get();
      if (doc.exists) {
        return Hotel.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting hotel: $e');
      return null;
    }
  }

  // Room availability checking
  Future<bool> checkRoomAvailability(
    String hotelId,
    String roomType,
    DateTime checkIn,
    DateTime checkOut,
  ) async {
    try {
      final querySnapshot = await _reservationsCollection
          .where('hotelId', isEqualTo: hotelId)
          .where('roomType', isEqualTo: roomType)
          .where('status', isEqualTo: 'confirmed')
          .get();

      for (final doc in querySnapshot.docs) {
        final reservation = Reservation.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id);
        
        // Check for date overlap
        if (checkIn.isBefore(reservation.checkOutDate) && 
            checkOut.isAfter(reservation.checkInDate)) {
          return false; // Room not available
        }
      }
      
      return true; // Room available
    } catch (e) {
      debugPrint('Error checking room availability: $e');
      return false;
    }
  }

  // Real-time streams
  Stream<List<Reservation>> getUserReservationsStream(String userId) {
    return _reservationsCollection
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Reservation.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id))
            .toList());
  }

  Stream<app_models.User?> getUserStream(String uid) {
    return _usersCollection
        .doc(uid)
        .snapshots()
        .map((doc) => doc.exists 
            ? app_models.User.fromFirestoreMap(doc.data() as Map<String, dynamic>, doc.id)
            : null);
  }
}
