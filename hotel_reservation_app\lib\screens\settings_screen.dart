import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_routes.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _emailNotifications = true;
  bool _pushNotifications = true;
  bool _darkMode = false;
  String _language = 'English';
  String _currency = 'USD';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      _emailNotifications = prefs.getBool('email_notifications') ?? true;
      _pushNotifications = prefs.getBool('push_notifications') ?? true;
      _darkMode = prefs.getBool('dark_mode') ?? false;
      _language = prefs.getString('language') ?? 'English';
      _currency = prefs.getString('currency') ?? 'USD';
    });
  }

  Future<void> _saveSetting(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();
    if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is String) {
      await prefs.setString(key, value);
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.settings,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 8),
                Text(
                  'App Settings',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Customize your app experience',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Notifications Section
          _buildSectionCard(
            'Notifications',
            Icons.notifications,
            [
              _buildSwitchTile(
                'Enable Notifications',
                'Receive notifications about bookings and updates',
                _notificationsEnabled,
                (value) {
                  setState(() => _notificationsEnabled = value);
                  _saveSetting('notifications_enabled', value);
                  _showSnackBar(value ? 'Notifications enabled' : 'Notifications disabled');
                },
              ),
              _buildSwitchTile(
                'Email Notifications',
                'Receive booking confirmations via email',
                _emailNotifications,
                (value) {
                  setState(() => _emailNotifications = value);
                  _saveSetting('email_notifications', value);
                },
                enabled: _notificationsEnabled,
              ),
              _buildSwitchTile(
                'Push Notifications',
                'Receive push notifications on your device',
                _pushNotifications,
                (value) {
                  setState(() => _pushNotifications = value);
                  _saveSetting('push_notifications', value);
                },
                enabled: _notificationsEnabled,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Appearance Section
          _buildSectionCard(
            'Appearance',
            Icons.palette,
            [
              _buildSwitchTile(
                'Dark Mode',
                'Use dark theme for the app',
                _darkMode,
                (value) {
                  setState(() => _darkMode = value);
                  _saveSetting('dark_mode', value);
                  _showSnackBar('Theme will change on next app restart');
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Language & Region Section
          _buildSectionCard(
            'Language & Region',
            Icons.language,
            [
              _buildDropdownTile(
                'Language',
                'Select your preferred language',
                _language,
                ['English', 'Spanish', 'French', 'German', 'Italian'],
                (value) {
                  setState(() => _language = value!);
                  _saveSetting('language', value);
                  _showSnackBar('Language changed to $value');
                },
              ),
              _buildDropdownTile(
                'Currency',
                'Select your preferred currency',
                _currency,
                ['USD', 'EUR', 'GBP', 'JPY', 'CAD'],
                (value) {
                  setState(() => _currency = value!);
                  _saveSetting('currency', value);
                  _showSnackBar('Currency changed to $value');
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Account Section
          _buildSectionCard(
            'Account',
            Icons.account_circle,
            [
              _buildActionTile(
                'Privacy Policy',
                'View our privacy policy',
                Icons.privacy_tip,
                () => NavigationHelper.goToPrivacyPolicy(context),
              ),
              _buildActionTile(
                'Help & Support',
                'Get help and contact support',
                Icons.help,
                () => NavigationHelper.goToHelp(context),
              ),
              _buildActionTile(
                'Contact Us',
                'Get in touch with our team',
                Icons.contact_phone,
                () => NavigationHelper.goToContact(context),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // App Info Section
          _buildSectionCard(
            'App Information',
            Icons.info,
            [
              _buildInfoTile('Version', '1.0.0'),
              _buildInfoTile('Build', '100'),
              _buildInfoTile('Developer', 'Hotel Reservation Team'),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildSectionCard(String title, IconData icon, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged, {
    bool enabled = true,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(
        title,
        style: TextStyle(
          color: enabled
              ? Theme.of(context).colorScheme.onSurface
              : Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: enabled
              ? Theme.of(context).colorScheme.onSurface.withOpacity(0.6)
              : Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
        ),
      ),
      trailing: Switch(
        value: enabled ? value : false,
        onChanged: enabled ? onChanged : null,
      ),
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<String>(
        value: value,
        items: options.map((option) {
          return DropdownMenuItem<String>(
            value: option,
            child: Text(option),
          );
        }).toList(),
        onChanged: onChanged,
        underline: Container(),
      ),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile(String title, String value) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(title),
      trailing: Text(
        value,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        ),
      ),
    );
  }
}
