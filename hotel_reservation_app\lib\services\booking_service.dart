import 'package:flutter/foundation.dart';
import '../models/reservation_model.dart';
import '../models/hotel_model.dart';
import '../models/user_model.dart' as app_models;
import 'firestore_service.dart';
import 'payment_service.dart';
import 'email_service.dart';
import 'notification_service.dart';
import 'hotel_service.dart';

class BookingService {
  static final BookingService _instance = BookingService._internal();
  static BookingService get instance => _instance;
  BookingService._internal();

  final FirestoreService _firestoreService = FirestoreService.instance;
  final PaymentService _paymentService = PaymentService.instance;
  final EmailService _emailService = EmailService.instance;
  final NotificationService _notificationService = NotificationService.instance;
  final HotelService _hotelService = HotelService.instance;

  // Create a complete booking with payment processing
  Future<BookingResult> createBooking({
    required app_models.User user,
    required String hotelId,
    required String roomType,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int guests,
    String? specialRequests,
    bool processPayment = true,
  }) async {
    try {
      // 1. Validate booking data
      final validationResult = await _validateBooking(
        hotelId: hotelId,
        roomType: roomType,
        checkInDate: checkInDate,
        checkOutDate: checkOutDate,
        guests: guests,
      );

      if (!validationResult.isValid) {
        return BookingResult.error(validationResult.errorMessage!);
      }

      // 2. Get hotel and room details
      final hotel = await _hotelService.getHotel(hotelId);
      if (hotel == null) {
        return BookingResult.error('Hotel not found');
      }

      final roomTypeData = hotel.roomTypes[roomType];
      if (roomTypeData == null) {
        return BookingResult.error('Room type not found');
      }

      // 3. Calculate total amount
      final numberOfNights = checkOutDate.difference(checkInDate).inDays;
      final totalAmount = roomTypeData.pricePerNight * numberOfNights;

      // 4. Create reservation object
      final reservation = Reservation(
        userId: user.id!,
        hotelId: hotelId,
        roomType: roomType,
        checkInDate: checkInDate,
        checkOutDate: checkOutDate,
        guests: guests,
        totalAmount: totalAmount,
        specialRequests: specialRequests,
        createdAt: DateTime.now(),
      );

      // 5. Process payment if required
      String? paymentIntentId;
      String? customerId;
      
      if (processPayment) {
        final paymentResult = await _paymentService.processReservationPayment(
          reservation: reservation,
          customerEmail: user.email,
          customerName: user.name,
        );

        if (!paymentResult.isSuccess) {
          return BookingResult.error('Payment failed: ${paymentResult.message}');
        }

        paymentIntentId = paymentResult.paymentIntentId;
        customerId = paymentResult.customerId;
        
        // Update reservation with payment details
        reservation.paymentDetails = {
          'paymentIntentId': paymentIntentId,
          'customerId': customerId,
          'paymentStatus': 'completed',
          'paymentDate': DateTime.now().toIso8601String(),
        };
      }

      // 6. Save reservation to database
      final reservationId = await _firestoreService.createReservation(reservation);
      reservation.id = reservationId;

      // 7. Send confirmation email
      await _emailService.sendBookingConfirmationEmail(
        email: user.email,
        name: user.name,
        reservation: reservation,
        hotelName: hotel.name,
        hotelAddress: hotel.address,
      );

      // 8. Send push notification
      if (_notificationService.fcmToken != null) {
        await _notificationService.sendPushNotification(
          fcmToken: _notificationService.fcmToken!,
          title: 'Booking Confirmed!',
          body: 'Your reservation at ${hotel.name} has been confirmed.',
          data: {
            'type': 'booking_confirmation',
            'reservationId': reservationId,
            'hotelId': hotelId,
          },
        );
      }

      // 9. Schedule check-in reminder
      await _scheduleCheckInReminder(reservation, hotel, user);

      return BookingResult.success(
        'Booking created successfully',
        reservation,
        paymentIntentId,
      );
    } catch (e) {
      debugPrint('Error creating booking: $e');
      return BookingResult.error('Failed to create booking: $e');
    }
  }

  // Validate booking data
  Future<ValidationResult> _validateBooking({
    required String hotelId,
    required String roomType,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int guests,
  }) async {
    // Check dates
    if (checkInDate.isBefore(DateTime.now())) {
      return ValidationResult.invalid('Check-in date cannot be in the past');
    }

    if (checkOutDate.isBefore(checkInDate.add(const Duration(days: 1)))) {
      return ValidationResult.invalid('Check-out date must be at least one day after check-in');
    }

    // Check room availability
    final isAvailable = await _hotelService.checkRoomAvailability(
      hotelId: hotelId,
      roomType: roomType,
      checkIn: checkInDate,
      checkOut: checkOutDate,
    );

    if (!isAvailable) {
      return ValidationResult.invalid('Room is not available for selected dates');
    }

    // Check guest capacity
    final hotel = await _hotelService.getHotel(hotelId);
    if (hotel != null) {
      final roomTypeData = hotel.roomTypes[roomType];
      if (roomTypeData != null && guests > roomTypeData.maxGuests) {
        return ValidationResult.invalid('Number of guests exceeds room capacity');
      }
    }

    return ValidationResult.valid();
  }

  // Schedule check-in reminder
  Future<void> _scheduleCheckInReminder(
    Reservation reservation,
    Hotel hotel,
    app_models.User user,
  ) async {
    try {
      // Schedule email reminder for 1 day before check-in
      final reminderDate = reservation.checkInDate.subtract(const Duration(days: 1));
      
      if (reminderDate.isAfter(DateTime.now())) {
        // In a real app, you would use a job scheduler or cloud function
        // For now, we'll just send the reminder immediately if it's within 24 hours
        final hoursUntilCheckIn = reservation.checkInDate.difference(DateTime.now()).inHours;
        
        if (hoursUntilCheckIn <= 24 && hoursUntilCheckIn > 0) {
          await _emailService.sendCheckInReminderEmail(
            email: user.email,
            name: user.name,
            reservation: reservation,
            hotelName: hotel.name,
          );
        }
      }
    } catch (e) {
      debugPrint('Error scheduling check-in reminder: $e');
    }
  }

  // Cancel booking
  Future<BookingResult> cancelBooking({
    required String reservationId,
    required String reason,
    bool processRefund = true,
  }) async {
    try {
      // 1. Get reservation
      final reservation = await _firestoreService.getReservation(reservationId);
      if (reservation == null) {
        return BookingResult.error('Reservation not found');
      }

      // 2. Check if cancellation is allowed
      final canCancel = _canCancelReservation(reservation);
      if (!canCancel) {
        return BookingResult.error('Cancellation not allowed for this reservation');
      }

      // 3. Process refund if payment was made
      if (processRefund && reservation.paymentDetails != null) {
        final paymentIntentId = reservation.paymentDetails!['paymentIntentId'];
        if (paymentIntentId != null) {
          final refundResult = await _paymentService.refundPayment(
            paymentIntentId: paymentIntentId,
            reason: reason,
          );

          if (!refundResult.isSuccess) {
            return BookingResult.error('Refund failed: ${refundResult.message}');
          }
        }
      }

      // 4. Update reservation status
      await _firestoreService.cancelReservation(reservationId);

      // 5. Send cancellation confirmation
      // TODO: Implement cancellation email

      return BookingResult.success(
        'Booking cancelled successfully',
        reservation.copyWith(status: 'cancelled'),
        null,
      );
    } catch (e) {
      debugPrint('Error cancelling booking: $e');
      return BookingResult.error('Failed to cancel booking: $e');
    }
  }

  // Check if reservation can be cancelled
  bool _canCancelReservation(Reservation reservation) {
    // Allow cancellation up to 24 hours before check-in
    final cutoffTime = reservation.checkInDate.subtract(const Duration(hours: 24));
    return DateTime.now().isBefore(cutoffTime);
  }

  // Modify booking
  Future<BookingResult> modifyBooking({
    required String reservationId,
    DateTime? newCheckInDate,
    DateTime? newCheckOutDate,
    int? newGuests,
    String? newSpecialRequests,
  }) async {
    try {
      // 1. Get existing reservation
      final reservation = await _firestoreService.getReservation(reservationId);
      if (reservation == null) {
        return BookingResult.error('Reservation not found');
      }

      // 2. Check if modification is allowed
      final canModify = _canModifyReservation(reservation);
      if (!canModify) {
        return BookingResult.error('Modification not allowed for this reservation');
      }

      // 3. Validate new dates if provided
      if (newCheckInDate != null || newCheckOutDate != null) {
        final checkIn = newCheckInDate ?? reservation.checkInDate;
        final checkOut = newCheckOutDate ?? reservation.checkOutDate;
        
        final validationResult = await _validateBooking(
          hotelId: reservation.hotelId!,
          roomType: reservation.roomType,
          checkInDate: checkIn,
          checkOutDate: checkOut,
          guests: newGuests ?? reservation.guests,
        );

        if (!validationResult.isValid) {
          return BookingResult.error(validationResult.errorMessage!);
        }
      }

      // 4. Calculate new total if dates changed
      double? newTotalAmount;
      if (newCheckInDate != null || newCheckOutDate != null) {
        final hotel = await _hotelService.getHotel(reservation.hotelId!);
        if (hotel != null) {
          final roomTypeData = hotel.roomTypes[reservation.roomType];
          if (roomTypeData != null) {
            final checkIn = newCheckInDate ?? reservation.checkInDate;
            final checkOut = newCheckOutDate ?? reservation.checkOutDate;
            final numberOfNights = checkOut.difference(checkIn).inDays;
            newTotalAmount = roomTypeData.pricePerNight * numberOfNights;
          }
        }
      }

      // 5. Update reservation
      final updatedReservation = reservation.copyWith(
        checkInDate: newCheckInDate,
        checkOutDate: newCheckOutDate,
        guests: newGuests,
        specialRequests: newSpecialRequests,
        totalAmount: newTotalAmount,
      );

      await _firestoreService.updateReservation(updatedReservation);

      return BookingResult.success(
        'Booking modified successfully',
        updatedReservation,
        null,
      );
    } catch (e) {
      debugPrint('Error modifying booking: $e');
      return BookingResult.error('Failed to modify booking: $e');
    }
  }

  // Check if reservation can be modified
  bool _canModifyReservation(Reservation reservation) {
    // Allow modification up to 48 hours before check-in
    final cutoffTime = reservation.checkInDate.subtract(const Duration(hours: 48));
    return DateTime.now().isBefore(cutoffTime);
  }

  // Get user's bookings
  Future<List<Reservation>> getUserBookings(String userId) async {
    try {
      return await _firestoreService.getUserReservations(userId);
    } catch (e) {
      debugPrint('Error getting user bookings: $e');
      return [];
    }
  }

  // Get booking details
  Future<Reservation?> getBookingDetails(String reservationId) async {
    try {
      return await _firestoreService.getReservation(reservationId);
    } catch (e) {
      debugPrint('Error getting booking details: $e');
      return null;
    }
  }
}

class BookingResult {
  final bool isSuccess;
  final String message;
  final Reservation? reservation;
  final String? paymentIntentId;

  BookingResult._(this.isSuccess, this.message, this.reservation, this.paymentIntentId);

  factory BookingResult.success(String message, Reservation reservation, String? paymentIntentId) =>
      BookingResult._(true, message, reservation, paymentIntentId);
      
  factory BookingResult.error(String message) =>
      BookingResult._(false, message, null, null);
}

class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  ValidationResult._(this.isValid, this.errorMessage);

  factory ValidationResult.valid() => ValidationResult._(true, null);
  factory ValidationResult.invalid(String message) => ValidationResult._(false, message);
}
